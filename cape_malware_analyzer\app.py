#!/usr/bin/env python3
"""
CAPE Malware Analyzer - Standalone Web Service
Main Flask Application
"""

import os
import logging
import threading
from datetime import datetime
from pathlib import Path

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash
from flask_cors import CORS
from werkzeug.exceptions import RequestEntityTooLarge

from config import Config
from cape_client import CAPEClient
from models import (
    Analysis, AnalysisRequest, AnalysisMode, AnalysisStatus, 
    FileInfo, analysis_storage
)
from utils import (
    allowed_file, secure_save_file, get_file_info, parse_analysis_options,
    validate_analysis_options, cleanup_file, create_error_response,
    create_success_response, get_machine_display_name
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Global lock for thread safety
analysis_lock = threading.Lock()

def analyze_file_async(analysis: Analysis):
    """
    Analyze file asynchronously in background thread
    
    Args:
        analysis: Analysis object to process
    """
    file_path = None
    try:
        # Update status to running
        with analysis_lock:
            analysis.status = AnalysisStatus.RUNNING
        
        logger.info(f"Starting analysis {analysis.analysis_id}")
        
        # Create CAPE client
        cape_client = CAPEClient()
        
        # For this async function, we need to reconstruct the file path
        # In a real implementation, you might want to store the file path in the analysis
        # For now, we'll assume the file is already processed and we have the hash
        
        # Check for existing analysis unless forced reanalysis
        task_id = None
        was_cached = False
        
        if not analysis.request.force_reanalyze and analysis.result.file_hash:
            logger.info("Checking for existing analysis...")
            task_id = cape_client.search_existing_analysis(analysis.result.file_hash)
            if task_id:
                was_cached = True
                logger.info(f"Found existing analysis: Task ID {task_id}")
        
        # If no existing analysis found, submit new task
        if task_id is None:
            logger.info("No existing analysis found, submitting new task...")
            # Note: In real implementation, we need to handle file submission differently
            # For now, we'll simulate this part
            logger.warning("File submission in async mode not fully implemented")
            with analysis_lock:
                analysis.status = AnalysisStatus.FAILED
                analysis.result.error_message = "Async file submission not implemented"
            return
        
        # Update analysis with task info
        with analysis_lock:
            analysis.update_result(
                cape_task_id=task_id,
                was_cached=was_cached
            )
        
        # Wait for completion if not cached
        if not was_cached:
            logger.info(f"Waiting for analysis completion: Task ID {task_id}")
            if not cape_client.wait_for_completion(task_id):
                # Check if task is still running vs actually failed
                current_status = cape_client.check_status(task_id)
                logger.warning(f"Analysis timeout reached. Current status: {current_status}")
                
                if current_status in ['pending', 'running', 'processing']:
                    # Task is still active, keep as processing
                    with analysis_lock:
                        analysis.status = AnalysisStatus.PROCESSING
                        analysis.result.error_message = "Analysis is still being processed on CAPE server"
                    return
                else:
                    # Task actually failed
                    with analysis_lock:
                        analysis.status = AnalysisStatus.FAILED
                        analysis.result.error_message = "Analysis did not complete in time"
                    return
        
        # Get report
        logger.info("Retrieving analysis report...")
        report = cape_client.get_report(task_id, 'json')
        
        if report is None:
            # Check if it's because task is still being analyzed
            current_status = cape_client.check_status(task_id)
            if current_status in ['pending', 'running', 'processing']:
                with analysis_lock:
                    analysis.status = AnalysisStatus.PROCESSING
                    analysis.result.error_message = "Analysis is still being processed on CAPE server"
                return
            else:
                with analysis_lock:
                    analysis.status = AnalysisStatus.FAILED
                    analysis.result.error_message = "Failed to retrieve report"
                return
        
        # Filter report fields
        filtered_report = cape_client.filter_report_fields(report)
        
        # Update analysis with results
        with analysis_lock:
            analysis.status = AnalysisStatus.REPORTED
            analysis.result.report = filtered_report
        
        logger.info(f"Analysis {analysis.analysis_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error in async analysis {analysis.analysis_id}: {e}")
        with analysis_lock:
            analysis.status = AnalysisStatus.FAILED
            analysis.result.error_message = str(e)
    finally:
        # Clean up file if it exists
        if file_path:
            cleanup_file(file_path)

# Web Interface Routes

@app.route('/')
def index():
    """Main page with upload form"""
    try:
        # Get available machines
        cape_client = CAPEClient()
        machines = cape_client.get_machines()
        
        # Add "First Available" option
        machine_options = [{'name': 'first_available', 'label': 'First Available'}]
        for machine in machines:
            machine_options.append({
                'name': machine.get('name', ''),
                'label': machine.get('label', machine.get('name', '')),
                'platform': machine.get('platform', ''),
                'status': machine.get('status', ''),
                'locked': machine.get('locked', False)
            })
        
        return render_template('index.html',
                             machines=machine_options,
                             advanced_options=Config.ADVANCED_OPTIONS,
                             default_options=Config.DEFAULT_OPTIONS)
    except Exception as e:
        logger.error(f"Error loading index page: {e}")
        flash('Error loading page. Please check CAPE server connection.')
        return render_template('index.html', machines=[], advanced_options={}, default_options={})

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and start analysis"""
    try:
        # Validate file upload
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if not allowed_file(file.filename):
            flash('File type not allowed')
            return redirect(request.url)
        
        # Save uploaded file
        file_path = secure_save_file(file, Config.UPLOAD_FOLDER)
        if not file_path:
            flash('Error saving file')
            return redirect(request.url)
        
        # Get file information
        file_info = get_file_info(file_path)
        
        # Get analysis parameters
        mode_str = request.form.get('analysis_mode', 'default')
        mode = AnalysisMode.ADVANCED if mode_str == 'advanced' else AnalysisMode.DEFAULT
        
        # Parse options based on mode
        if mode == AnalysisMode.ADVANCED:
            options = {}
            for option_key in Config.ADVANCED_OPTIONS:
                if option_key in request.form:
                    value = request.form[option_key]
                    if value:  # Only add non-empty values
                        options[option_key] = value
            options = validate_analysis_options(options)
        else:
            options = Config.DEFAULT_OPTIONS.copy()
        
        machine = request.form.get('machine', 'first_available')
        force_reanalyze = request.form.get('force_reanalyze') == 'on'
        
        # Create analysis request
        analysis_request = AnalysisRequest(
            analysis_id="",  # Will be auto-generated
            filename=file.filename,
            file_info=file_info,
            mode=mode,
            options=options,
            machine=machine,
            force_reanalyze=force_reanalyze
        )
        
        # Create analysis object
        analysis = Analysis(analysis_request)
        analysis.result.file_hash = file_info.md5
        
        # Store analysis
        with analysis_lock:
            analysis_storage.store(analysis)
        
        # Start analysis in background thread
        thread = threading.Thread(
            target=analyze_file_async,
            args=(analysis,)
        )
        thread.daemon = True
        thread.start()
        
        # Clean up uploaded file (we have the hash now)
        cleanup_file(file_path)
        
        flash(f'File uploaded successfully! Analysis ID: {analysis.analysis_id}')
        return redirect(url_for('analysis_status', analysis_id=analysis.analysis_id))
        
    except Exception as e:
        logger.error(f"Error in file upload: {e}")
        flash('Error processing file upload')
        return redirect(request.url)

# API Routes

@app.route('/api/v1/analyze', methods=['POST'])
def api_analyze():
    """
    API endpoint to submit file for analysis

    Supports both Mode 1 (default) and Mode 2 (advanced) analysis
    """
    try:
        # Validate file upload
        if 'file' not in request.files:
            return jsonify(create_error_response('No file provided'))

        file = request.files['file']
        if file.filename == '':
            return jsonify(create_error_response('No file selected'))

        if not allowed_file(file.filename):
            return jsonify(create_error_response('File type not allowed'))

        # Save uploaded file
        file_path = secure_save_file(file, Config.UPLOAD_FOLDER)
        if not file_path:
            return jsonify(create_error_response('Error saving file'))

        try:
            # Get file information
            file_info = get_file_info(file_path)

            # Determine analysis mode
            mode_str = request.form.get('mode', 'default').lower()
            mode = AnalysisMode.ADVANCED if mode_str == 'advanced' else AnalysisMode.DEFAULT

            # Parse analysis options
            options = {}
            if mode == AnalysisMode.ADVANCED:
                # Get options from form data or options string
                options_str = request.form.get('options', '')
                if options_str:
                    options = parse_analysis_options(options_str)

                # Also check individual form fields
                for option_key in Config.ADVANCED_OPTIONS:
                    if option_key in request.form:
                        value = request.form[option_key]
                        if value:
                            options[option_key] = value

                options = validate_analysis_options(options)
            else:
                # Use default options for Mode 1
                options = Config.DEFAULT_OPTIONS.copy()

            # Get other parameters
            machine = request.form.get('machine', 'first_available')
            force_reanalyze = request.form.get('force_reanalyze', '').lower() in ['true', '1', 'yes']

            # Create CAPE client for immediate submission
            cape_client = CAPEClient()

            # Check for existing analysis unless forced reanalysis
            task_id = None
            was_cached = False

            if not force_reanalyze and file_info.md5:
                logger.info("Checking for existing analysis...")
                task_id = cape_client.search_existing_analysis(file_info.md5)
                if task_id:
                    was_cached = True
                    logger.info(f"Found existing analysis: Task ID {task_id}")

            # If no existing analysis found, submit new task
            if task_id is None:
                logger.info("Submitting new analysis...")
                task_id = cape_client.submit_file(file_path, options, machine)
                if not task_id:
                    return jsonify(create_error_response('Failed to submit file to CAPE'))

            # Create analysis request
            analysis_request = AnalysisRequest(
                analysis_id="",  # Will be auto-generated
                filename=file.filename,
                file_info=file_info,
                mode=mode,
                options=options,
                machine=machine,
                force_reanalyze=force_reanalyze
            )

            # Create analysis object
            analysis = Analysis(analysis_request)
            analysis.status = AnalysisStatus.RUNNING
            analysis.update_result(
                cape_task_id=task_id,
                file_hash=file_info.md5,
                was_cached=was_cached
            )

            # Store analysis
            with analysis_lock:
                analysis_storage.store(analysis)

            # Start monitoring in background thread
            thread = threading.Thread(
                target=monitor_analysis_async,
                args=(analysis.analysis_id, task_id, was_cached)
            )
            thread.daemon = True
            thread.start()

            return jsonify(create_success_response({
                'analysis_id': analysis.analysis_id,
                'message': 'File submitted for analysis',
                'cape_task_id': task_id,
                'was_cached': was_cached,
                'status_url': url_for('api_status', analysis_id=analysis.analysis_id, _external=True),
                'report_url': url_for('api_report', analysis_id=analysis.analysis_id, _external=True)
            }))

        finally:
            # Clean up uploaded file
            cleanup_file(file_path)

    except Exception as e:
        logger.error(f"Error in API analyze: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}'))

def monitor_analysis_async(analysis_id: str, task_id: int, was_cached: bool):
    """
    Monitor analysis progress in background thread

    Args:
        analysis_id: Analysis ID
        task_id: CAPE task ID
        was_cached: Whether analysis was cached
    """
    try:
        cape_client = CAPEClient()

        # If cached, try to get report immediately
        if was_cached:
            logger.info(f"Getting cached report for task {task_id}")
            report = cape_client.get_report(task_id, 'json')
            if report:
                filtered_report = cape_client.filter_report_fields(report)
                with analysis_lock:
                    analysis = analysis_storage.get(analysis_id)
                    if analysis:
                        analysis.status = AnalysisStatus.REPORTED
                        analysis.result.report = filtered_report
                return

        # Wait for completion
        logger.info(f"Waiting for analysis completion: Task ID {task_id}")
        if cape_client.wait_for_completion(task_id):
            # Get report
            report = cape_client.get_report(task_id, 'json')
            if report:
                filtered_report = cape_client.filter_report_fields(report)
                with analysis_lock:
                    analysis = analysis_storage.get(analysis_id)
                    if analysis:
                        analysis.status = AnalysisStatus.REPORTED
                        analysis.result.report = filtered_report
                logger.info(f"Analysis {analysis_id} completed successfully")
            else:
                with analysis_lock:
                    analysis = analysis_storage.get(analysis_id)
                    if analysis:
                        analysis.status = AnalysisStatus.PROCESSING
                        analysis.result.error_message = "Report not yet available"
        else:
            # Check current status
            current_status = cape_client.check_status(task_id)
            with analysis_lock:
                analysis = analysis_storage.get(analysis_id)
                if analysis:
                    if current_status in ['pending', 'running', 'processing']:
                        analysis.status = AnalysisStatus.PROCESSING
                        analysis.result.error_message = "Analysis is still being processed"
                    else:
                        analysis.status = AnalysisStatus.FAILED
                        analysis.result.error_message = "Analysis failed or timed out"

    except Exception as e:
        logger.error(f"Error monitoring analysis {analysis_id}: {e}")
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)
            if analysis:
                analysis.status = AnalysisStatus.FAILED
                analysis.result.error_message = str(e)

@app.route('/api/v1/status/<analysis_id>')
def api_status(analysis_id):
    """API endpoint to check analysis status"""
    try:
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)

        if not analysis:
            return jsonify(create_error_response('Analysis not found'))

        # If status is processing, try to check if report is now available
        if analysis.status == AnalysisStatus.PROCESSING and analysis.result.cape_task_id:
            try:
                cape_client = CAPEClient()
                current_status = cape_client.check_status(analysis.result.cape_task_id)

                # Map CAPE status to our status
                mapped_status = Config.STATUS_MAPPING.get(current_status, current_status)

                if mapped_status == 'reported':
                    # Try to get the report
                    report = cape_client.get_report(analysis.result.cape_task_id, 'json')
                    if report and not report.get('error'):
                        filtered_report = cape_client.filter_report_fields(report)
                        with analysis_lock:
                            analysis.status = AnalysisStatus.REPORTED
                            analysis.result.report = filtered_report
                            analysis.result.error_message = None
            except Exception as e:
                logger.warning(f"Error checking processing status: {e}")

        return jsonify(analysis.to_status_response())

    except Exception as e:
        logger.error(f"Error in API status: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}'))

@app.route('/api/v1/report/<analysis_id>')
def api_report(analysis_id):
    """API endpoint to get analysis report"""
    try:
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)

        if not analysis:
            return jsonify(create_error_response('Analysis not found'))

        if analysis.status != AnalysisStatus.REPORTED:
            return jsonify(create_error_response('Analysis not completed'))

        return jsonify(analysis.to_report_response())

    except Exception as e:
        logger.error(f"Error in API report: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}'))

@app.route('/api/v1/machines')
def api_machines():
    """API endpoint to get available machines"""
    try:
        cape_client = CAPEClient()
        machines = cape_client.get_machines()

        # Format machines for API response
        machine_list = [{'name': 'first_available', 'label': 'First Available'}]
        for machine in machines:
            machine_list.append({
                'name': machine.get('name', ''),
                'label': machine.get('label', machine.get('name', '')),
                'platform': machine.get('platform', ''),
                'status': machine.get('status', ''),
                'locked': machine.get('locked', False)
            })

        return jsonify(create_success_response({
            'machines': machine_list
        }))

    except Exception as e:
        logger.error(f"Error getting machines: {e}")
        return jsonify(create_error_response(
            f'Failed to get machines: {str(e)}',
            {'machines': [{'name': 'first_available', 'label': 'First Available'}]}
        ))

# Additional Web Interface Routes

@app.route('/status/<analysis_id>')
def analysis_status(analysis_id):
    """Show analysis status page"""
    try:
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)

        if not analysis:
            flash('Analysis not found')
            return redirect(url_for('index'))

        # If status is processing, try to check if report is now available
        if analysis.status == AnalysisStatus.PROCESSING and analysis.result.cape_task_id:
            try:
                cape_client = CAPEClient()
                current_status = cape_client.check_status(analysis.result.cape_task_id)
                mapped_status = Config.STATUS_MAPPING.get(current_status, current_status)

                if mapped_status == 'reported':
                    report = cape_client.get_report(analysis.result.cape_task_id, 'json')
                    if report and not report.get('error'):
                        filtered_report = cape_client.filter_report_fields(report)
                        with analysis_lock:
                            analysis.status = AnalysisStatus.REPORTED
                            analysis.result.report = filtered_report
                            analysis.result.error_message = None
            except Exception as e:
                logger.warning(f"Error checking processing status: {e}")

        return render_template('status.html', analysis=analysis)

    except Exception as e:
        logger.error(f"Error in status page: {e}")
        flash('Error loading status page')
        return redirect(url_for('index'))

@app.route('/report/<analysis_id>')
def view_report(analysis_id):
    """View analysis report"""
    try:
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)

        if not analysis:
            flash('Analysis not found')
            return redirect(url_for('index'))

        if analysis.status != AnalysisStatus.REPORTED or not analysis.result.report:
            flash('Analysis not completed or report not available')
            return redirect(url_for('analysis_status', analysis_id=analysis_id))

        return render_template('report.html',
                             analysis=analysis,
                             report=analysis.result.report)

    except Exception as e:
        logger.error(f"Error in report page: {e}")
        flash('Error loading report page')
        return redirect(url_for('analysis_status', analysis_id=analysis_id))

@app.route('/analyses')
def list_analyses():
    """List all analyses"""
    try:
        with analysis_lock:
            analyses = analysis_storage.list_all()

        # Sort by creation time, newest first
        analyses.sort(key=lambda x: x.request.created_at or datetime.min, reverse=True)

        return render_template('analyses.html', analyses=analyses)

    except Exception as e:
        logger.error(f"Error in analyses list: {e}")
        flash('Error loading analyses list')
        return render_template('analyses.html', analyses=[])

# Error Handlers

@app.errorhandler(413)
def too_large(e):
    """Handle file too large error"""
    flash('File too large. Maximum size is 500MB.')
    return redirect(url_for('index'))

@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors"""
    return jsonify(create_error_response('Endpoint not found')), 404

@app.errorhandler(500)
def internal_error(e):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {e}")
    return jsonify(create_error_response('Internal server error')), 500

if __name__ == '__main__':
    print("=" * 60)
    print("CAPE Malware Analyzer - Standalone Web Service")
    print("=" * 60)
    print(f"CAPE Server: {Config.CAPE_URL}")
    print(f"Upload folder: {Config.UPLOAD_FOLDER}")
    print(f"Web interface: http://{Config.HOST}:{Config.PORT}")
    print(f"API base URL: http://{Config.HOST}:{Config.PORT}/api/v1")
    print()
    print("API Endpoints:")
    print("  POST /api/v1/analyze - Submit file for analysis")
    print("  GET /api/v1/status/<analysis_id> - Check analysis status")
    print("  GET /api/v1/report/<analysis_id> - Get analysis report")
    print("  GET /api/v1/machines - List available machines")
    print()
    print("Web Interface:")
    print("  GET / - Main upload interface")
    print("  GET /status/<analysis_id> - Status page")
    print("  GET /report/<analysis_id> - Report viewer")
    print("  GET /analyses - List all analyses")
    print("=" * 60)

    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)

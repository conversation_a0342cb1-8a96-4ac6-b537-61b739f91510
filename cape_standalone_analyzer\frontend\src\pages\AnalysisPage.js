import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw, 
  FileText, 
  ArrowLeft,
  ExternalLink
} from 'lucide-react';

import { apiService, pollAnalysisStatus } from '../services/api';
import StatusBadge from '../components/StatusBadge';

const AnalysisPage = () => {
  const { analysisId } = useParams();
  const navigate = useNavigate();
  const [analysisData, setAnalysisData] = useState(null);
  const [isPolling, setIsPolling] = useState(false);
  const [error, setError] = useState(null);

  // Initial data fetch
  const { data: initialData, isLoading, error: fetchError } = useQuery(
    ['analysis', analysisId],
    () => apiService.getAnalysisStatus(analysisId),
    {
      enabled: !!analysisId,
      onSuccess: (data) => {
        setAnalysisData(data.data);
        
        // Start polling if analysis is not complete
        const status = data.data?.status;
        if (status && !['reported', 'failed', 'cancelled'].includes(status)) {
          startPolling();
        }
      },
      onError: (error) => {
        setError(error.message);
      }
    }
  );

  const startPolling = () => {
    if (isPolling) return;
    
    setIsPolling(true);
    pollAnalysisStatus(
      analysisId,
      (data) => {
        if (data.error) {
          setError(data.error);
          setIsPolling(false);
        } else {
          setAnalysisData(data);
          
          // Stop polling if analysis is complete
          if (['reported', 'failed', 'cancelled'].includes(data.status)) {
            setIsPolling(false);
          }
        }
      }
    ).catch((error) => {
      setError(error.message);
      setIsPolling(false);
    });
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'running':
        return <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'processing':
        return <RefreshCw className="h-5 w-5 text-purple-600 animate-spin" />;
      case 'reported':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'cancelled':
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const calculateDuration = (startTime, endTime) => {
    if (!startTime) return 'N/A';
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffMs = end - start;
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    
    if (diffMins > 0) {
      return `${diffMins}m ${diffSecs}s`;
    }
    return `${diffSecs}s`;
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <div className="spinner mr-3"></div>
          <span className="text-lg">Loading analysis...</span>
        </div>
      </div>
    );
  }

  if (fetchError || error) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="cape-card p-6">
          <div className="flex items-center text-red-600 mb-4">
            <XCircle className="h-6 w-6 mr-2" />
            <h2 className="text-xl font-semibold">Error</h2>
          </div>
          <p className="text-gray-700 mb-4">
            {fetchError?.message || error || 'Failed to load analysis data'}
          </p>
          <div className="flex space-x-4">
            <button
              onClick={() => navigate('/')}
              className="cape-button-primary"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </button>
            <button
              onClick={() => window.location.reload()}
              className="cape-button-secondary"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!analysisData) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="cape-card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Analysis Not Found</h2>
          <p className="text-gray-700 mb-4">
            The requested analysis could not be found.
          </p>
          <Link to="/" className="cape-button-primary">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  const { status, filename, file_info, message, cape_task_id, created_at, started_at, completed_at, updated_at } = analysisData;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analysis Status</h1>
          <p className="text-gray-600">Analysis ID: {analysisId}</p>
        </div>
        <div className="flex space-x-3">
          <Link to="/" className="cape-button-secondary">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          {status === 'reported' && (
            <Link to={`/report/${analysisId}`} className="cape-button-primary">
              <FileText className="h-4 w-4 mr-2" />
              View Report
            </Link>
          )}
        </div>
      </div>

      {/* Status Card */}
      <div className="cape-card p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            {getStatusIcon(status)}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                <StatusBadge status={status} />
              </h2>
              <p className="text-gray-600">{message}</p>
            </div>
          </div>
          
          {isPolling && (
            <div className="flex items-center text-blue-600">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              <span className="text-sm">Auto-refreshing...</span>
            </div>
          )}
        </div>

        {/* Progress indicator for running analyses */}
        {['pending', 'running', 'processing'].includes(status) && (
          <div className="mb-6">
            <div className="progress-bar">
              <div 
                className={`progress-bar-fill ${
                  status === 'pending' ? 'w-1/4' : 
                  status === 'running' ? 'w-2/4' : 
                  'w-3/4'
                }`}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Submitted</span>
              <span>Running</span>
              <span>Processing</span>
              <span>Complete</span>
            </div>
          </div>
        )}

        {/* File Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">File Information</h3>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-gray-500">Filename:</dt>
                <dd className="text-gray-900 font-medium">{filename}</dd>
              </div>
              {file_info && (
                <>
                  <div className="flex justify-between">
                    <dt className="text-gray-500">Size:</dt>
                    <dd className="text-gray-900">{(file_info.size / 1024).toFixed(1)} KB</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-500">MD5:</dt>
                    <dd className="text-gray-900 font-mono text-xs">{file_info.md5}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-500">SHA256:</dt>
                    <dd className="text-gray-900 font-mono text-xs">{file_info.sha256}</dd>
                  </div>
                </>
              )}
            </dl>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Analysis Timeline</h3>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-gray-500">Submitted:</dt>
                <dd className="text-gray-900">{formatDateTime(created_at)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Started:</dt>
                <dd className="text-gray-900">{formatDateTime(started_at)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Completed:</dt>
                <dd className="text-gray-900">{formatDateTime(completed_at)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Duration:</dt>
                <dd className="text-gray-900">{calculateDuration(started_at, completed_at)}</dd>
              </div>
              {cape_task_id && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">CAPE Task ID:</dt>
                  <dd className="text-gray-900">{cape_task_id}</dd>
                </div>
              )}
            </dl>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-center space-x-4">
        {status === 'reported' && (
          <Link to={`/report/${analysisId}`} className="cape-button-primary">
            <FileText className="h-4 w-4 mr-2" />
            View Full Report
          </Link>
        )}
        
        {!isPolling && !['reported', 'failed', 'cancelled'].includes(status) && (
          <button
            onClick={startPolling}
            className="cape-button-secondary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Resume Monitoring
          </button>
        )}
        
        <Link to="/analyses" className="cape-button-secondary">
          <ExternalLink className="h-4 w-4 mr-2" />
          View All Analyses
        </Link>
      </div>

      {/* Auto-refresh notice */}
      {isPolling && (
        <div className="alert alert-info">
          <div className="flex items-center">
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            <span>
              This page will automatically update every 5 seconds until the analysis is complete.
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisPage;

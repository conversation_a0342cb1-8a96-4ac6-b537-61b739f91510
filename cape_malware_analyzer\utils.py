"""
Utility functions for CAPE Malware Analyzer
"""

import os
import hashlib
import magic
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from werkzeug.utils import secure_filename

from config import Config
from models import FileInfo

logger = logging.getLogger(__name__)

def allowed_file(filename: str) -> bool:
    """Check if file extension is allowed"""
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in Config.ALLOWED_EXTENSIONS

def secure_save_file(file, upload_folder: Path) -> Optional[Path]:
    """
    Securely save uploaded file
    
    Args:
        file: Uploaded file object
        upload_folder: Directory to save file
        
    Returns:
        Path to saved file or None if failed
    """
    try:
        if not file or not file.filename:
            return None
        
        if not allowed_file(file.filename):
            logger.warning(f"File type not allowed: {file.filename}")
            return None
        
        # Create secure filename
        filename = secure_filename(file.filename)
        if not filename:
            logger.warning("Could not create secure filename")
            return None
        
        # Create unique filename to avoid conflicts
        import uuid
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = upload_folder / unique_filename
        
        # Save file
        file.save(str(file_path))
        logger.info(f"File saved: {file_path}")
        
        return file_path
        
    except Exception as e:
        logger.error(f"Error saving file: {e}")
        return None

def get_file_info(file_path: Path) -> FileInfo:
    """
    Get comprehensive file information
    
    Args:
        file_path: Path to file
        
    Returns:
        FileInfo object with file details
    """
    try:
        stat = file_path.stat()
        
        # Calculate hashes
        md5_hash = calculate_hash(file_path, 'md5')
        sha1_hash = calculate_hash(file_path, 'sha1')
        sha256_hash = calculate_hash(file_path, 'sha256')
        
        # Get MIME type
        mime_type = None
        try:
            mime_type = magic.from_file(str(file_path), mime=True)
        except Exception as e:
            logger.debug(f"Could not determine MIME type: {e}")
        
        return FileInfo(
            name=file_path.name,
            size=stat.st_size,
            md5=md5_hash,
            sha1=sha1_hash,
            sha256=sha256_hash,
            mime_type=mime_type
        )
        
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        return FileInfo(
            name=file_path.name if file_path else "unknown",
            size=0
        )

def calculate_hash(file_path: Path, hash_type: str = 'md5') -> Optional[str]:
    """
    Calculate file hash
    
    Args:
        file_path: Path to file
        hash_type: Type of hash (md5, sha1, sha256)
        
    Returns:
        Hash string or None if failed
    """
    try:
        hash_func = getattr(hashlib, hash_type)()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating {hash_type} hash: {e}")
        return None

def parse_analysis_options(options_str: str) -> Dict[str, str]:
    """
    Parse analysis options string
    
    Args:
        options_str: Options string like "procmemdump=1,unpacker=2"
        
    Returns:
        Dictionary of parsed options
    """
    options = {}
    
    if not options_str:
        return options
    
    try:
        for option in options_str.split(','):
            option = option.strip()
            if '=' in option:
                key, value = option.split('=', 1)
                options[key.strip()] = value.strip()
    except Exception as e:
        logger.warning(f"Error parsing options string '{options_str}': {e}")
    
    return options

def validate_analysis_options(options: Dict[str, str]) -> Dict[str, str]:
    """
    Validate and filter analysis options
    
    Args:
        options: Raw options dictionary
        
    Returns:
        Validated options dictionary
    """
    validated = {}
    
    for key, value in options.items():
        if key in Config.ADVANCED_OPTIONS:
            option_config = Config.ADVANCED_OPTIONS[key]
            
            if option_config['type'] == 'checkbox':
                # Convert various true/false representations
                if str(value).lower() in ['1', 'true', 'yes', 'on']:
                    validated[key] = '1'
                elif str(value).lower() in ['0', 'false', 'no', 'off']:
                    validated[key] = '0'
            elif option_config['type'] == 'select':
                # Validate against allowed options
                if value in option_config.get('options', {}):
                    validated[key] = value
                else:
                    logger.warning(f"Invalid option value for {key}: {value}")
            else:
                # For other types, just pass through
                validated[key] = str(value)
        else:
            logger.warning(f"Unknown analysis option: {key}")
    
    return validated

def cleanup_file(file_path: Path):
    """
    Safely remove file
    
    Args:
        file_path: Path to file to remove
    """
    try:
        if file_path and file_path.exists():
            file_path.unlink()
            logger.debug(f"Cleaned up file: {file_path}")
    except Exception as e:
        logger.warning(f"Could not cleanup file {file_path}: {e}")

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def get_machine_display_name(machine_name: str, machines: List[Dict[str, Any]]) -> str:
    """
    Get display name for machine
    
    Args:
        machine_name: Machine name
        machines: List of available machines
        
    Returns:
        Display name for machine
    """
    if machine_name == 'first_available':
        return 'First Available'
    
    for machine in machines:
        if machine.get('name') == machine_name:
            return machine.get('label', machine_name)
    
    return machine_name

def create_error_response(message: str, details: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Create standardized error response
    
    Args:
        message: Error message
        details: Additional error details
        
    Returns:
        Error response dictionary
    """
    response = {
        'error': True,
        'message': message
    }
    
    if details:
        response.update(details)
    
    return response

def create_success_response(data: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Create standardized success response
    
    Args:
        data: Response data
        
    Returns:
        Success response dictionary
    """
    response = {'error': False}
    
    if data:
        response.update(data)
    
    return response

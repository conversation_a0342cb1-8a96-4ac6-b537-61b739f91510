import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.message || 'An error occurred';
      throw new Error(errorMessage);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network error - please check your connection');
    } else {
      // Something else happened
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }
);

// API service methods
export const apiService = {
  // Health check
  healthCheck: () => api.get('/health'),

  // Get available machines
  getMachines: () => api.get('/machines'),

  // Get configuration
  getConfig: () => api.get('/config'),

  // Submit file for analysis
  submitAnalysis: (formData) => {
    return api.post('/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // Longer timeout for file uploads
    });
  },

  // Get analysis status
  getAnalysisStatus: (analysisId) => api.get(`/status/${analysisId}`),

  // Get analysis report
  getAnalysisReport: (analysisId) => api.get(`/report/${analysisId}`),

  // List all analyses
  listAnalyses: () => api.get('/analyses'),
};

// Utility functions for polling
export const pollAnalysisStatus = async (analysisId, onUpdate, maxAttempts = 360) => {
  let attempts = 0;
  
  const poll = async () => {
    try {
      const response = await apiService.getAnalysisStatus(analysisId);
      const status = response.data?.status;
      
      if (onUpdate) {
        onUpdate(response.data);
      }
      
      // Check if analysis is complete
      if (status === 'reported' || status === 'failed' || status === 'cancelled') {
        return response.data;
      }
      
      // Continue polling if not complete and under max attempts
      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(poll, 5000); // Poll every 5 seconds
      } else {
        throw new Error('Polling timeout - analysis taking too long');
      }
    } catch (error) {
      if (onUpdate) {
        onUpdate({ error: error.message });
      }
      throw error;
    }
  };
  
  return poll();
};

export default api;

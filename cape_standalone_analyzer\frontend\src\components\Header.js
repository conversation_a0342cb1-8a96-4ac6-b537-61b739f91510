import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Shield, Home, List, Activity } from 'lucide-react';

const Header = () => {
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <header className="cape-gradient shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Title */}
          <Link to="/" className="flex items-center space-x-3 text-white hover:text-gray-200 transition-colors">
            <Shield className="h-8 w-8" />
            <div>
              <h1 className="text-xl font-bold">CAPE Standalone Analyzer</h1>
              <p className="text-xs text-gray-200">Malware Analysis Platform</p>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="flex items-center space-x-6">
            <Link
              to="/"
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/')
                  ? 'bg-white bg-opacity-20 text-white'
                  : 'text-gray-200 hover:text-white hover:bg-white hover:bg-opacity-10'
              }`}
            >
              <Home className="h-4 w-4" />
              <span>Home</span>
            </Link>

            <Link
              to="/analyses"
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/analyses')
                  ? 'bg-white bg-opacity-20 text-white'
                  : 'text-gray-200 hover:text-white hover:bg-white hover:bg-opacity-10'
              }`}
            >
              <List className="h-4 w-4" />
              <span>Analyses</span>
            </Link>

            {/* Status indicator */}
            <div className="flex items-center space-x-2 text-gray-200">
              <Activity className="h-4 w-4" />
              <span className="text-xs">Online</span>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;

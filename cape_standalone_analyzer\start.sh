#!/bin/bash

# CAPE Standalone Analyzer - Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEFAULT_CAPE_URL="http://localhost:8000"
DEFAULT_SERVICE_PORT="8080"

echo -e "${BLUE}================================================${NC}"
echo -e "${BLUE}    CAPE Standalone Analyzer - Startup${NC}"
echo -e "${BLUE}================================================${NC}"
echo

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed or not in PATH${NC}"
    echo "Please install Docker and try again."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed or not in PATH${NC}"
    echo "Please install Docker Compose and try again."
    exit 1
fi

# Function to prompt for configuration
prompt_config() {
    echo -e "${YELLOW}Configuration Setup${NC}"
    echo "Please provide the following configuration:"
    echo

    # CAPE URL
    read -p "CAPE v2 URL [$DEFAULT_CAPE_URL]: " CAPE_URL
    CAPE_URL=${CAPE_URL:-$DEFAULT_CAPE_URL}

    # Service Port
    read -p "Service Port [$DEFAULT_SERVICE_PORT]: " SERVICE_PORT
    SERVICE_PORT=${SERVICE_PORT:-$DEFAULT_SERVICE_PORT}

    # Secret Key
    SECRET_KEY=$(openssl rand -hex 32 2>/dev/null || echo "change-this-secret-key-in-production")

    echo
    echo -e "${GREEN}Configuration Summary:${NC}"
    echo "  CAPE URL: $CAPE_URL"
    echo "  Service Port: $SERVICE_PORT"
    echo "  Secret Key: [Generated]"
    echo
}

# Function to create .env file
create_env_file() {
    echo -e "${YELLOW}Creating environment configuration...${NC}"
    
    cat > .env << EOF
# CAPE Standalone Analyzer Configuration
CAPE_URL=$CAPE_URL
CAPE_TIMEOUT=300
CAPE_MAX_WAIT=1800

# Backend Configuration
FLASK_ENV=production
SECRET_KEY=$SECRET_KEY
BACKEND_PORT=5000

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
FRONTEND_PORT=3000

# Service Configuration
SERVICE_PORT=$SERVICE_PORT

# Security
ALLOWED_EXTENSIONS=exe,dll,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar,7z,tar,gz,apk,jar,bat,cmd,ps1,vbs,js,html,htm

# Upload Limits
MAX_FILE_SIZE=100MB
UPLOAD_TIMEOUT=300

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/cape_analyzer.log
EOF

    echo -e "${GREEN}Environment file created: .env${NC}"
}

# Function to test CAPE connection
test_cape_connection() {
    echo -e "${YELLOW}Testing connection to CAPE v2...${NC}"
    
    if curl -s --connect-timeout 5 "$CAPE_URL/apiv2/cuckoo/status/" > /dev/null; then
        echo -e "${GREEN}✓ CAPE v2 is accessible at $CAPE_URL${NC}"
    else
        echo -e "${RED}✗ Cannot connect to CAPE v2 at $CAPE_URL${NC}"
        echo -e "${YELLOW}Warning: Please ensure CAPE v2 is running and accessible${NC}"
        echo
        read -p "Continue anyway? (y/N): " continue_anyway
        if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
            echo "Startup cancelled."
            exit 1
        fi
    fi
}

# Function to start services
start_services() {
    echo -e "${YELLOW}Starting CAPE Standalone Analyzer...${NC}"
    echo

    # Build and start services
    echo "Building Docker images..."
    docker-compose build

    echo "Starting services..."
    docker-compose up -d

    # Wait for services to be ready
    echo "Waiting for services to start..."
    sleep 10

    # Check service health
    echo -e "${YELLOW}Checking service health...${NC}"
    
    # Check backend
    if curl -s "http://localhost:5000/api/v1/health" > /dev/null; then
        echo -e "${GREEN}✓ Backend service is running${NC}"
    else
        echo -e "${RED}✗ Backend service is not responding${NC}"
    fi

    # Check frontend
    if curl -s "http://localhost:3000" > /dev/null; then
        echo -e "${GREEN}✓ Frontend service is running${NC}"
    else
        echo -e "${RED}✗ Frontend service is not responding${NC}"
    fi

    # Check nginx proxy
    if curl -s "http://localhost:$SERVICE_PORT/health" > /dev/null; then
        echo -e "${GREEN}✓ Nginx proxy is running${NC}"
    else
        echo -e "${RED}✗ Nginx proxy is not responding${NC}"
    fi
}

# Function to show service information
show_service_info() {
    echo
    echo -e "${GREEN}================================================${NC}"
    echo -e "${GREEN}    CAPE Standalone Analyzer - Ready!${NC}"
    echo -e "${GREEN}================================================${NC}"
    echo
    echo -e "${BLUE}Service URLs:${NC}"
    echo "  Web Interface: http://localhost:$SERVICE_PORT"
    echo "  Backend API:   http://localhost:5000/api/v1"
    echo "  Frontend:      http://localhost:3000"
    echo
    echo -e "${BLUE}Management Commands:${NC}"
    echo "  View logs:     docker-compose logs -f"
    echo "  Stop services: docker-compose down"
    echo "  Restart:       docker-compose restart"
    echo
    echo -e "${BLUE}API Endpoints:${NC}"
    echo "  Health Check:  GET  /api/v1/health"
    echo "  Submit File:   POST /api/v1/analyze"
    echo "  Get Status:    GET  /api/v1/status/{analysis_id}"
    echo "  Get Report:    GET  /api/v1/report/{analysis_id}"
    echo "  List Analyses: GET  /api/v1/analyses"
    echo
    echo -e "${YELLOW}Note: Make sure CAPE v2 is running at $CAPE_URL${NC}"
    echo
}

# Main execution
main() {
    # Check if .env file exists
    if [[ ! -f .env ]]; then
        prompt_config
        create_env_file
    else
        echo -e "${YELLOW}Using existing .env configuration${NC}"
        source .env
        SERVICE_PORT=${SERVICE_PORT:-$DEFAULT_SERVICE_PORT}
    fi

    # Test CAPE connection
    test_cape_connection

    # Start services
    start_services

    # Show service information
    show_service_info
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "CAPE Standalone Analyzer Startup Script"
        echo
        echo "Usage: $0 [options]"
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --config       Reconfigure settings"
        echo "  --stop         Stop all services"
        echo "  --restart      Restart all services"
        echo "  --logs         Show service logs"
        echo
        exit 0
        ;;
    --config)
        prompt_config
        create_env_file
        echo -e "${GREEN}Configuration updated. Run '$0' to start services.${NC}"
        exit 0
        ;;
    --stop)
        echo -e "${YELLOW}Stopping CAPE Standalone Analyzer...${NC}"
        docker-compose down
        echo -e "${GREEN}Services stopped.${NC}"
        exit 0
        ;;
    --restart)
        echo -e "${YELLOW}Restarting CAPE Standalone Analyzer...${NC}"
        docker-compose restart
        echo -e "${GREEN}Services restarted.${NC}"
        exit 0
        ;;
    --logs)
        docker-compose logs -f
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo -e "${RED}Unknown option: $1${NC}"
        echo "Use '$0 --help' for usage information."
        exit 1
        ;;
esac

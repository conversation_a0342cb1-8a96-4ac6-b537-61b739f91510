# Deployment Guide

This guide covers different deployment options for the CAPE Standalone Analyzer.

## Quick Start (Docker Compose)

The easiest way to deploy the CAPE Standalone Analyzer is using Docker Compose.

### Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Running CAPE v2 instance
- 4GB+ RAM recommended
- 10GB+ disk space

### Linux/macOS Deployment

1. **Clone or extract the project**:
   ```bash
   cd cape_standalone_analyzer
   ```

2. **Run the startup script**:
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

3. **Follow the configuration prompts**:
   - Enter your CAPE v2 URL (e.g., `http://localhost:8000`)
   - Choose a service port (default: `8080`)
   - The script will generate a secure secret key

4. **Access the service**:
   - Web Interface: http://localhost:8080
   - API: http://localhost:8080/api/v1

### Windows Deployment

1. **Open Command Prompt as Administrator**

2. **Navigate to the project directory**:
   ```cmd
   cd cape_standalone_analyzer
   ```

3. **Run the startup script**:
   ```cmd
   start.bat
   ```

4. **Follow the configuration prompts** (same as Linux)

### Manual Docker Compose

If you prefer manual configuration:

1. **Copy the environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file**:
   ```bash
   nano .env
   ```
   
   Update these key variables:
   ```env
   CAPE_URL=http://your-cape-server:8000
   SECRET_KEY=your-secure-secret-key
   SERVICE_PORT=8080
   ```

3. **Start the services**:
   ```bash
   docker-compose up -d
   ```

4. **Check service status**:
   ```bash
   docker-compose ps
   docker-compose logs
   ```

## Production Deployment

### Environment Configuration

For production deployments, ensure you configure these critical settings:

```env
# Security
SECRET_KEY=your-very-secure-random-secret-key
FLASK_ENV=production

# CAPE Configuration
CAPE_URL=https://your-cape-server.com
CAPE_TIMEOUT=600
CAPE_MAX_WAIT=3600

# Resource Limits
MAX_FILE_SIZE=500MB
UPLOAD_TIMEOUT=600

# Logging
LOG_LEVEL=INFO
LOG_FILE=/app/logs/cape_analyzer.log
```

### SSL/TLS Configuration

For production, you should use SSL/TLS. Update the nginx configuration:

1. **Create SSL certificates** (Let's Encrypt recommended)

2. **Update nginx.conf**:
   ```nginx
   server {
       listen 443 ssl http2;
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
       # ... rest of configuration
   }
   ```

3. **Update docker-compose.yml**:
   ```yaml
   nginx:
     ports:
       - "443:443"
     volumes:
       - ./ssl:/etc/ssl/certs:ro
   ```

### Reverse Proxy Setup

If deploying behind a reverse proxy (recommended):

#### Nginx Reverse Proxy

```nginx
upstream cape_analyzer {
    server localhost:8080;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://cape_analyzer;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # File upload support
        client_max_body_size 500M;
        proxy_read_timeout 300s;
    }
}
```

#### Apache Reverse Proxy

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    
    ProxyPreserveHost On
    ProxyPass / http://localhost:8080/
    ProxyPassReverse / http://localhost:8080/
    
    # File upload support
    LimitRequestBody 524288000
</VirtualHost>
```

### Database Configuration (Optional)

For persistent storage, you can configure a database:

1. **Add database service to docker-compose.yml**:
   ```yaml
   postgres:
     image: postgres:15
     environment:
       POSTGRES_DB: cape_analyzer
       POSTGRES_USER: cape_user
       POSTGRES_PASSWORD: secure_password
     volumes:
       - postgres_data:/var/lib/postgresql/data
   ```

2. **Update backend environment**:
   ```env
   DATABASE_URL=****************************************************/cape_analyzer
   ```

### Redis Configuration (Optional)

For improved performance and session management:

1. **Add Redis service**:
   ```yaml
   redis:
     image: redis:7-alpine
     volumes:
       - redis_data:/data
   ```

2. **Update backend environment**:
   ```env
   REDIS_URL=redis://redis:6379/0
   ```

## Monitoring and Maintenance

### Health Checks

Monitor service health using these endpoints:

- **Overall Health**: `GET /health`
- **Backend Health**: `GET /api/v1/health`
- **Service Status**: `docker-compose ps`

### Log Management

View logs using Docker Compose:

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f nginx
```

### Backup and Recovery

#### Backup

```bash
# Backup uploaded files
docker cp cape_analyzer_backend_1:/app/uploads ./backup/uploads

# Backup logs
docker cp cape_analyzer_backend_1:/app/logs ./backup/logs

# Backup configuration
cp .env ./backup/
```

#### Recovery

```bash
# Restore uploaded files
docker cp ./backup/uploads cape_analyzer_backend_1:/app/

# Restore configuration
cp ./backup/.env ./
```

### Updates

To update the CAPE Standalone Analyzer:

1. **Stop services**:
   ```bash
   docker-compose down
   ```

2. **Pull latest images**:
   ```bash
   docker-compose pull
   ```

3. **Rebuild if needed**:
   ```bash
   docker-compose build --no-cache
   ```

4. **Start services**:
   ```bash
   docker-compose up -d
   ```

## Troubleshooting

### Common Issues

1. **Cannot connect to CAPE v2**:
   - Verify CAPE_URL is correct
   - Check network connectivity
   - Ensure CAPE v2 API is accessible

2. **File upload fails**:
   - Check MAX_FILE_SIZE setting
   - Verify disk space
   - Check nginx client_max_body_size

3. **Services won't start**:
   - Check Docker daemon is running
   - Verify port availability
   - Check logs: `docker-compose logs`

4. **Frontend not loading**:
   - Check if backend is running
   - Verify API URL configuration
   - Check browser console for errors

### Performance Tuning

1. **Increase worker processes** (backend):
   ```yaml
   backend:
     command: gunicorn --workers 4 --bind 0.0.0.0:5000 app:app
   ```

2. **Optimize nginx**:
   ```nginx
   worker_processes auto;
   worker_connections 1024;
   ```

3. **Resource limits**:
   ```yaml
   backend:
     deploy:
       resources:
         limits:
           memory: 2G
           cpus: '1.0'
   ```

## Security Considerations

1. **Change default secret key**
2. **Use HTTPS in production**
3. **Implement rate limiting**
4. **Regular security updates**
5. **Network isolation**
6. **File type validation**
7. **Input sanitization**

For additional support, check the main README.md or create an issue in the project repository.

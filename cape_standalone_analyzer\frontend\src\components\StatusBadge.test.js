import { render, screen } from '@testing-library/react';
import StatusBadge from './StatusBadge';

describe('StatusBadge', () => {
  test('renders pending status', () => {
    render(<StatusBadge status="pending" />);
    const badge = screen.getByText(/pending/i);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('cape-status-pending');
  });

  test('renders running status', () => {
    render(<StatusBadge status="running" />);
    const badge = screen.getByText(/running/i);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('cape-status-running');
  });

  test('renders completed status', () => {
    render(<StatusBadge status="reported" />);
    const badge = screen.getByText(/completed/i);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('cape-status-reported');
  });

  test('renders failed status', () => {
    render(<StatusBadge status="failed" />);
    const badge = screen.getByText(/failed/i);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('cape-status-failed');
  });

  test('renders without icon when showIcon is false', () => {
    render(<StatusBadge status="pending" showIcon={false} />);
    const badge = screen.getByText(/pending/i);
    expect(badge).toBeInTheDocument();
    
    // Check that no icon is present (icons have specific classes)
    const icon = badge.querySelector('svg');
    expect(icon).not.toBeInTheDocument();
  });

  test('renders with different sizes', () => {
    const { rerender } = render(<StatusBadge status="pending" size="small" />);
    let badge = screen.getByText(/pending/i);
    expect(badge).toHaveClass('text-xs', 'px-2', 'py-0.5');

    rerender(<StatusBadge status="pending" size="large" />);
    badge = screen.getByText(/pending/i);
    expect(badge).toHaveClass('text-sm', 'px-3', 'py-1');
  });

  test('handles unknown status', () => {
    render(<StatusBadge status="unknown" />);
    const badge = screen.getByText(/unknown/i);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('cape-status-cancelled');
  });
});

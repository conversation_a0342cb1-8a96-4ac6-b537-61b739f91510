# CAPE Standalone Analyzer Configuration

# Cape v2 Configuration
CAPE_URL=http://localhost:8000
CAPE_TIMEOUT=300
CAPE_MAX_WAIT=1800

# Backend Configuration
FLASK_ENV=production
SECRET_KEY=your-secret-key-here-change-this-in-production
BACKEND_PORT=5000

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
FRONTEND_PORT=3000

# Service Configuration
SERVICE_PORT=8080

# Security
ALLOWED_EXTENSIONS=exe,dll,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar,7z,tar,gz,apk,jar,bat,cmd,ps1,vbs,js,html,htm

# Upload Limits
MAX_FILE_SIZE=100MB
UPLOAD_TIMEOUT=300

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/cape_analyzer.log

# Database (optional - for persistent storage)
# DATABASE_URL=sqlite:///cape_analyzer.db

# Redis (optional - for caching and session storage)
# REDIS_URL=redis://localhost:6379/0

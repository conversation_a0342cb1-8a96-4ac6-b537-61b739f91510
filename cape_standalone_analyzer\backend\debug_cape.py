#!/usr/bin/env python3
"""
Debug script for CAPE API connection
"""
import os
import requests
import json
from cape_client import CapeClient

def test_cape_endpoints():
    """Test various CAPE API endpoints"""
    cape_url = os.environ.get('CAPE_URL', 'http://localhost:8000')
    print(f"Testing CAPE API at: {cape_url}")
    print("=" * 50)
    
    # Test 1: Basic connection
    print("1. Testing basic connection...")
    try:
        response = requests.get(f"{cape_url}/apiv2/cuckoo/status/", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection failed: {e}")
    
    print()
    
    # Test 2: Get machines
    print("2. Testing machines endpoint...")
    try:
        response = requests.get(f"{cape_url}/apiv2/cuckoo/machines/", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            machines = response.json()
            print(f"   Found {len(machines.get('data', []))} machines")
            for machine in machines.get('data', [])[:3]:  # Show first 3
                print(f"     - {machine.get('name')} ({machine.get('platform')})")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Request failed: {e}")
    
    print()
    
    # Test 3: Test with CapeClient
    print("3. Testing with CapeClient...")
    try:
        client = CapeClient(cape_url=cape_url, verbose=True)
        
        # Test connection
        connected = client.test_connection()
        print(f"   Connection test: {'✓ PASS' if connected else '✗ FAIL'}")
        
        # Test get machines
        machines = client.get_machines()
        print(f"   Machines: {len(machines)} found")
        
    except Exception as e:
        print(f"   CapeClient error: {e}")
    
    print()
    
    # Test 4: Test task status endpoint format
    print("4. Testing task status endpoint format...")
    try:
        # Try to get a task that might not exist to see error format
        response = requests.get(f"{cape_url}/apiv2/tasks/view/999999/", timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Response structure: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"   Request failed: {e}")

if __name__ == "__main__":
    test_cape_endpoints()

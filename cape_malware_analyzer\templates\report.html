{% extends "base.html" %}

{% block title %}Analysis Report - CAPE Malware Analyzer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    Analysis Report: {{ analysis.request.filename }}
                </h3>
            </div>
            <div class="card-body">
                <!-- Report Summary -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>File Information</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Filename:</strong></td>
                                <td>{{ analysis.request.filename }}</td>
                            </tr>
                            <tr>
                                <td><strong>Analysis ID:</strong></td>
                                <td class="file-hash">{{ analysis.analysis_id }}</td>
                            </tr>
                            {% if analysis.result.cape_task_id %}
                            <tr>
                                <td><strong>CAPE Task ID:</strong></td>
                                <td>{{ analysis.result.cape_task_id }}</td>
                            </tr>
                            {% endif %}
                            {% if analysis.result.file_hash %}
                            <tr>
                                <td><strong>MD5:</strong></td>
                                <td class="file-hash">{{ analysis.result.file_hash }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Analysis Details</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td><span class="badge bg-success">Completed</span></td>
                            </tr>
                            <tr>
                                <td><strong>Completed:</strong></td>
                                <td>{{ analysis.result.completed_at.strftime('%Y-%m-%d %H:%M:%S') if analysis.result.completed_at else 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Cached:</strong></td>
                                <td>
                                    {% if analysis.result.was_cached %}
                                        <span class="badge bg-info">Yes</span>
                                    {% else %}
                                        <span class="badge bg-secondary">No</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Report Navigation -->
                <div class="mb-4">
                    <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                        {% set sections = [] %}
                        {% if report.info %}{% set _ = sections.append(('info', 'Info', 'fas fa-info-circle')) %}{% endif %}
                        {% if report.target %}{% set _ = sections.append(('target', 'Target', 'fas fa-bullseye')) %}{% endif %}
                        {% if report.signatures %}{% set _ = sections.append(('signatures', 'Signatures', 'fas fa-shield-alt')) %}{% endif %}
                        {% if report.behavior %}{% set _ = sections.append(('behavior', 'Behavior', 'fas fa-eye')) %}{% endif %}
                        {% if report.network %}{% set _ = sections.append(('network', 'Network', 'fas fa-network-wired')) %}{% endif %}
                        {% if report.CAPE %}{% set _ = sections.append(('cape', 'CAPE', 'fas fa-magic')) %}{% endif %}
                        {% if report.statistics %}{% set _ = sections.append(('statistics', 'Statistics', 'fas fa-chart-bar')) %}{% endif %}
                        {% set _ = sections.append(('raw', 'Raw JSON', 'fas fa-code')) %}
                        
                        {% for section_id, section_name, section_icon in sections %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {% if loop.first %}active{% endif %}" 
                                    id="{{ section_id }}-tab" 
                                    data-bs-toggle="tab" 
                                    data-bs-target="#{{ section_id }}" 
                                    type="button" 
                                    role="tab">
                                <i class="{{ section_icon }} me-1"></i>{{ section_name }}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>

                <!-- Report Content -->
                <div class="tab-content" id="reportTabsContent">
                    {% if report.info %}
                    <div class="tab-pane fade show active" id="info" role="tabpanel">
                        <div class="report-section">
                            <h4>Analysis Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        {% if report.info.id %}
                                        <tr><td><strong>Task ID:</strong></td><td>{{ report.info.id }}</td></tr>
                                        {% endif %}
                                        {% if report.info.started %}
                                        <tr><td><strong>Started:</strong></td><td>{{ report.info.started }}</td></tr>
                                        {% endif %}
                                        {% if report.info.ended %}
                                        <tr><td><strong>Ended:</strong></td><td>{{ report.info.ended }}</td></tr>
                                        {% endif %}
                                        {% if report.info.duration %}
                                        <tr><td><strong>Duration:</strong></td><td>{{ report.info.duration }}s</td></tr>
                                        {% endif %}
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        {% if report.info.score %}
                                        <tr><td><strong>Score:</strong></td><td><span class="badge bg-warning">{{ report.info.score }}/10</span></td></tr>
                                        {% endif %}
                                        {% if report.info.platform %}
                                        <tr><td><strong>Platform:</strong></td><td>{{ report.info.platform }}</td></tr>
                                        {% endif %}
                                        {% if report.info.version %}
                                        <tr><td><strong>Version:</strong></td><td>{{ report.info.version }}</td></tr>
                                        {% endif %}
                                        {% if report.info.machine and report.info.machine.name %}
                                        <tr><td><strong>Machine:</strong></td><td>{{ report.info.machine.name }}</td></tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if report.target %}
                    <div class="tab-pane fade {% if not report.info %}show active{% endif %}" id="target" role="tabpanel">
                        <div class="report-section">
                            <h4>Target File</h4>
                            {% if report.target.file %}
                            <table class="table">
                                {% for key, value in report.target.file.items() %}
                                <tr>
                                    <td><strong>{{ key.title() }}:</strong></td>
                                    <td class="{% if key in ['md5', 'sha1', 'sha256'] %}file-hash{% endif %}">{{ value }}</td>
                                </tr>
                                {% endfor %}
                            </table>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% if report.signatures %}
                    <div class="tab-pane fade" id="signatures" role="tabpanel">
                        <div class="report-section">
                            <h4>Detection Signatures ({{ report.signatures|length }})</h4>
                            {% for signature in report.signatures %}
                            <div class="card mb-2">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <span class="badge bg-danger me-2">{{ signature.severity or 'Unknown' }}</span>
                                        {{ signature.name or signature.description }}
                                    </h6>
                                    {% if signature.description and signature.description != signature.name %}
                                    <p class="card-text">{{ signature.description }}</p>
                                    {% endif %}
                                    {% if signature.marks %}
                                    <small class="text-muted">Marks: {{ signature.marks|length }}</small>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    {% if report.behavior %}
                    <div class="tab-pane fade" id="behavior" role="tabpanel">
                        <div class="report-section">
                            <h4>Behavioral Analysis</h4>
                            {% if report.behavior.processes %}
                            <h6>Processes ({{ report.behavior.processes|length }})</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>PID</th>
                                            <th>Name</th>
                                            <th>Parent PID</th>
                                            <th>First Seen</th>
                                            <th>Calls</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for process in report.behavior.processes[:10] %}
                                        <tr>
                                            <td>{{ process.process_id }}</td>
                                            <td>{{ process.process_name }}</td>
                                            <td>{{ process.parent_id }}</td>
                                            <td>{{ process.first_seen }}</td>
                                            <td>{{ process.calls|length if process.calls else 0 }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% if report.behavior.processes|length > 10 %}
                            <small class="text-muted">Showing first 10 of {{ report.behavior.processes|length }} processes</small>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% if report.network %}
                    <div class="tab-pane fade" id="network" role="tabpanel">
                        <div class="report-section">
                            <h4>Network Activity</h4>
                            {% if report.network.tcp %}
                            <h6>TCP Connections ({{ report.network.tcp|length }})</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr><th>Source</th><th>Destination</th><th>Port</th></tr>
                                    </thead>
                                    <tbody>
                                        {% for conn in report.network.tcp[:10] %}
                                        <tr>
                                            <td>{{ conn.src }}</td>
                                            <td>{{ conn.dst }}</td>
                                            <td>{{ conn.dport }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% endif %}
                            
                            {% if report.network.dns %}
                            <h6>DNS Requests ({{ report.network.dns|length }})</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr><th>Request</th><th>Type</th><th>Answer</th></tr>
                                    </thead>
                                    <tbody>
                                        {% for dns in report.network.dns[:10] %}
                                        <tr>
                                            <td>{{ dns.request }}</td>
                                            <td>{{ dns.type }}</td>
                                            <td>{{ dns.answers|join(', ') if dns.answers else '' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% if report.CAPE %}
                    <div class="tab-pane fade" id="cape" role="tabpanel">
                        <div class="report-section">
                            <h4>CAPE Analysis</h4>
                            <pre class="json-viewer"><code>{{ report.CAPE | tojson(indent=2) }}</code></pre>
                        </div>
                    </div>
                    {% endif %}

                    {% if report.statistics %}
                    <div class="tab-pane fade" id="statistics" role="tabpanel">
                        <div class="report-section">
                            <h4>Statistics</h4>
                            <pre class="json-viewer"><code>{{ report.statistics | tojson(indent=2) }}</code></pre>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Raw JSON Tab -->
                    <div class="tab-pane fade" id="raw" role="tabpanel">
                        <div class="report-section">
                            <h4>Raw JSON Report</h4>
                            <div class="mb-3">
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport()">
                                    <i class="fas fa-download me-1"></i>Download JSON
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="copyReport()">
                                    <i class="fas fa-copy me-1"></i>Copy to Clipboard
                                </button>
                            </div>
                            <pre class="json-viewer" id="rawJson"><code>{{ report | tojson(indent=2) }}</code></pre>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-4 d-flex gap-2">
                    <a href="{{ url_for('analysis_status', analysis_id=analysis.analysis_id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Status
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-upload me-2"></i>Analyze Another File
                    </a>
                    <a href="{{ url_for('list_analyses') }}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>All Analyses
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function downloadReport() {
    const reportData = {{ report | tojson }};
    const blob = new Blob([JSON.stringify(reportData, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cape_report_{{ analysis.analysis_id }}.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function copyReport() {
    const reportText = document.getElementById('rawJson').textContent;
    navigator.clipboard.writeText(reportText).then(function() {
        alert('Report copied to clipboard!');
    });
}
</script>
{% endblock %}

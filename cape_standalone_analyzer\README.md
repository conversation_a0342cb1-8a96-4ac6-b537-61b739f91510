# CAPE Standalone Analyzer

A standalone web service that leverages the Cape v2 API to provide a user-friendly and reliable platform for malware analysis. This service is completely separate from the Cape v2 front-end to avoid any conflicts and ensure flexibility.

## 🚀 Features

### Core Components

- **Backend Web Service**: RESTful API for file uploads and analysis requests
- **Frontend Interface**: Modern React-based web interface
- **Independent Deployment**: Runs on its own port, separate from Cape v2
- **Complete Integration**: Direct communication with Cape v2 API

### Analysis Capabilities

- **Two Analysis Modes**:
  - **Default Mode**: Simple analysis with optimized settings (like cape_simple_service.py)
  - **Advanced Mode**: Full customization options (like web/submission/views.py)
- **Machine Selection**: "First Available" option or specific VM selection
- **Real-time Status**: Live updates on analysis progress
- **Complete Reports**: Full analysis reports with all Cape v2 fields

### Technical Features

- **Robust Task Management**: Proper status transitions (pending → running → processing → reported)
- **Error Handling**: Graceful handling of "task still being analyzed" scenarios
- **Report Mirroring**: Interface matches Cape v2 report page for familiarity
- **Single Deployment**: Backend and frontend deployed as one unit

## 📁 Project Structure

```
cape_standalone_analyzer/
├── backend/                 # Flask-based REST API
│   ├── app.py              # Main application
│   ├── config.py           # Configuration
│   ├── models.py           # Data models
│   ├── cape_client.py      # Cape v2 API client
│   ├── utils.py            # Utility functions
│   └── requirements.txt    # Python dependencies
├── frontend/               # React web interface
│   ├── src/               # Source code
│   ├── public/            # Static assets
│   ├── package.json       # Node.js dependencies
│   └── build/             # Production build
├── docs/                  # Documentation
└── docker-compose.yml     # Deployment configuration
```

## 🛠️ Quick Start

### Prerequisites

- Cape v2 instance running and accessible
- Docker 20.10+ and Docker Compose 2.0+
- 4GB+ RAM recommended
- 10GB+ disk space

### Easy Installation (Recommended)

#### Linux/macOS
```bash
cd cape_standalone_analyzer
chmod +x start.sh
./start.sh
```

#### Windows
```cmd
cd cape_standalone_analyzer
start.bat
```

The startup script will:
- Guide you through configuration
- Test CAPE v2 connectivity
- Build and start all services
- Provide access URLs and management commands

### Manual Installation

1. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your CAPE URL and settings
   ```

2. **Start Services**:
   ```bash
   docker-compose up -d
   ```

3. **Access the Service**:
   - **Web Interface**: http://localhost:8080
   - **API Documentation**: http://localhost:8080/api/v1
   - **Backend API**: http://localhost:5000/api/v1
   - **Frontend**: http://localhost:3000

### Development Setup

See [Development Guide](docs/development.md) for detailed development setup instructions.

## 🔧 Configuration

### Environment Variables

- `CAPE_URL`: Cape v2 server URL (default: http://localhost:8000)
- `CAPE_TIMEOUT`: Analysis timeout in seconds (default: 300)
- `CAPE_MAX_WAIT`: Maximum wait time in seconds (default: 1800)
- `BACKEND_PORT`: Backend service port (default: 5000)
- `FRONTEND_PORT`: Frontend service port (default: 3000)

## 📖 API Documentation

### Endpoints

- `POST /api/v1/analyze` - Submit file for analysis
- `GET /api/v1/status/{analysis_id}` - Check analysis status
- `GET /api/v1/report/{analysis_id}` - Get analysis report
- `GET /api/v1/machines` - List available machines

### Usage Examples

See [API Documentation](docs/api.md) for detailed examples.

## 🧪 Testing

```bash
# Backend tests
cd backend
python -m pytest tests/

# Frontend tests
cd frontend
npm test
```

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Development Guide](docs/development.md)
- [Configuration Reference](docs/configuration.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the same license as Cape v2.

## 🆘 Support

For issues and questions:
1. Check the [documentation](docs/)
2. Search existing issues
3. Create a new issue with detailed information

---

**Note**: This is a standalone service that communicates with Cape v2 via its API. Ensure your Cape v2 instance is properly configured and accessible.

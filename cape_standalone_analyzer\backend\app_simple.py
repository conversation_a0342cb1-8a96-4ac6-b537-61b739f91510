#!/usr/bin/env python3
"""
Simple CAPE Standalone Analyzer Backend - For testing without real CAPE
"""
import os
import uuid
import time
import hashlib
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# In-memory storage for demo
analyses = {}

def get_file_hash(file_content):
    """Get SHA256 hash of file content"""
    return hashlib.sha256(file_content).hexdigest()

@app.route('/api/v1/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Backend is running (Simple Mode)',
        'cape_connected': False,  # We're not using real CAPE
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0-simple'
    })

@app.route('/api/v1/machines', methods=['GET'])
def get_machines():
    """Get available machines (mock data)"""
    machines = [
        {'name': 'first_available', 'label': 'First Available', 'platform': 'Auto'},
        {'name': 'windows10', 'label': 'Windows 10 (Windows)', 'platform': 'Windows', 'status': 'available'},
        {'name': 'ubuntu20', 'label': 'Ubuntu 20.04 (Linux)', 'platform': 'Linux', 'status': 'available'},
    ]
    
    return jsonify({
        'success': True,
        'data': machines
    })

@app.route('/api/v1/config', methods=['GET'])
def get_config():
    """Get configuration"""
    return jsonify({
        'success': True,
        'data': {
            'analysis_modes': [
                {'value': 'default', 'label': 'Default (Recommended)'},
                {'value': 'advanced', 'label': 'Advanced (Custom Options)'}
            ],
            'allowed_extensions': ['exe', 'dll', 'pdf', 'doc', 'docx', 'zip', 'rar', 'apk', 'jar'],
            'max_file_size': 100 * 1024 * 1024,  # 100MB
            'cape_url': 'http://localhost:8000'
        }
    })

@app.route('/api/v1/analyze', methods=['POST'])
def analyze():
    """Submit file for analysis"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'No file provided'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'No file selected'
            }), 400
        
        # Read file content
        file_content = file.read()
        file.seek(0)  # Reset file pointer
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Get file info
        file_hash = get_file_hash(file_content)
        file_info = {
            'filename': file.filename,
            'size': len(file_content),
            'md5': hashlib.md5(file_content).hexdigest(),
            'sha1': hashlib.sha1(file_content).hexdigest(),
            'sha256': file_hash,
            'mime_type': file.content_type or 'application/octet-stream'
        }
        
        # Save file
        filename = f"{analysis_id}_{file.filename}"
        filepath = os.path.join('uploads', filename)
        os.makedirs('uploads', exist_ok=True)
        file.save(filepath)
        
        # Create analysis record
        analysis = {
            'analysis_id': analysis_id,
            'filename': file.filename,
            'file_info': file_info,
            'status': 'pending',
            'message': 'Analysis queued',
            'cape_task_id': None,
            'created_at': datetime.utcnow().isoformat(),
            'started_at': None,
            'completed_at': None,
            'updated_at': datetime.utcnow().isoformat(),
            'error': None,
            'report': None
        }
        
        analyses[analysis_id] = analysis
        
        # Simulate analysis progression in background
        import threading
        def simulate_analysis():
            time.sleep(2)  # Simulate processing time
            
            # Update to running
            analyses[analysis_id]['status'] = 'running'
            analyses[analysis_id]['message'] = 'Analysis running'
            analyses[analysis_id]['started_at'] = datetime.utcnow().isoformat()
            analyses[analysis_id]['cape_task_id'] = 12345  # Mock task ID
            
            time.sleep(5)  # Simulate analysis time
            
            # Update to completed
            analyses[analysis_id]['status'] = 'reported'
            analyses[analysis_id]['message'] = 'Analysis completed successfully'
            analyses[analysis_id]['completed_at'] = datetime.utcnow().isoformat()
            analyses[analysis_id]['report'] = {
                'info': {
                    'id': 12345,
                    'started': analyses[analysis_id]['started_at'],
                    'ended': analyses[analysis_id]['completed_at']
                },
                'target': {
                    'file': file_info
                },
                'signatures': [
                    {
                        'name': 'mock_detection',
                        'description': 'Mock malware detection for testing',
                        'severity': 'medium'
                    }
                ],
                'malscore': 6.5,
                'behavior': {
                    'processes': [
                        {
                            'process_name': file.filename,
                            'pid': 1234
                        }
                    ]
                },
                'network': {
                    'dns': [{'request': 'example.com'}],
                    'http': []
                }
            }
        
        thread = threading.Thread(target=simulate_analysis)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': 'File submitted for analysis',
            'data': {
                'analysis_id': analysis_id,
                'filename': file.filename,
                'size': len(file_content),
                'status_url': f'/api/v1/status/{analysis_id}',
                'report_url': f'/api/v1/report/{analysis_id}'
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}'
        }), 500

@app.route('/api/v1/status/<analysis_id>', methods=['GET'])
def get_status(analysis_id):
    """Get analysis status"""
    try:
        if analysis_id not in analyses:
            return jsonify({
                'success': False,
                'message': 'Analysis not found'
            }), 404
        
        analysis = analyses[analysis_id]
        
        return jsonify({
            'success': True,
            'data': {
                'analysis_id': analysis_id,
                'filename': analysis['filename'],
                'status': analysis['status'],
                'message': analysis['message'],
                'cape_task_id': analysis['cape_task_id'],
                'file_info': analysis['file_info'],
                'created_at': analysis['created_at'],
                'started_at': analysis['started_at'],
                'completed_at': analysis['completed_at'],
                'updated_at': analysis['updated_at'],
                'error': analysis['error']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}'
        }), 500

@app.route('/api/v1/report/<analysis_id>', methods=['GET'])
def get_report(analysis_id):
    """Get analysis report"""
    try:
        if analysis_id not in analyses:
            return jsonify({
                'success': False,
                'message': 'Analysis not found'
            }), 404
        
        analysis = analyses[analysis_id]
        
        if analysis['status'] != 'reported':
            return jsonify({
                'success': False,
                'message': 'Analysis not completed'
            }), 400
        
        return jsonify({
            'success': True,
            'data': {
                'analysis_id': analysis_id,
                'filename': analysis['filename'],
                'status': analysis['status'],
                'message': analysis['message'],
                'cape_task_id': analysis['cape_task_id'],
                'file_info': analysis['file_info'],
                'created_at': analysis['created_at'],
                'started_at': analysis['started_at'],
                'completed_at': analysis['completed_at'],
                'updated_at': analysis['updated_at'],
                'report': analysis['report']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}'
        }), 500

@app.route('/api/v1/analyses', methods=['GET'])
def list_analyses():
    """List all analyses"""
    try:
        analyses_list = []
        for analysis_id, analysis in analyses.items():
            analyses_list.append({
                'analysis_id': analysis_id,
                'filename': analysis['filename'],
                'status': analysis['status'],
                'message': analysis['message'],
                'cape_task_id': analysis['cape_task_id'],
                'file_info': analysis['file_info'],
                'created_at': analysis['created_at'],
                'started_at': analysis['started_at'],
                'completed_at': analysis['completed_at'],
                'updated_at': analysis['updated_at'],
                'error': analysis['error']
            })
        
        # Sort by creation time (newest first)
        analyses_list.sort(key=lambda x: x['created_at'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': {
                'analyses': analyses_list,
                'total': len(analyses_list)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 CAPE Standalone Analyzer - Simple Backend")
    print("📡 API: http://localhost:5000/api/v1")
    print("🔗 Health: http://localhost:5000/api/v1/health")
    print("⚠️  Note: This is a mock backend for testing without real CAPE")
    print()
    
    app.run(host='0.0.0.0', port=5000, debug=True)

{% extends "base.html" %}

{% block title %}Analysis Status - CAPE Malware Analyzer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Analysis Status
                </h3>
            </div>
            <div class="card-body">
                <!-- Status Overview -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>File Information</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Filename:</strong></td>
                                <td>{{ analysis.request.filename }}</td>
                            </tr>
                            <tr>
                                <td><strong>Size:</strong></td>
                                <td>{{ "%.1f"|format(analysis.request.file_info.size / 1024 / 1024) }} MB</td>
                            </tr>
                            {% if analysis.request.file_info.md5 %}
                            <tr>
                                <td><strong>MD5:</strong></td>
                                <td class="file-hash">{{ analysis.request.file_info.md5 }}</td>
                            </tr>
                            {% endif %}
                            {% if analysis.request.file_info.mime_type %}
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td>{{ analysis.request.file_info.mime_type }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Analysis Information</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Analysis ID:</strong></td>
                                <td class="file-hash">{{ analysis.analysis_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Mode:</strong></td>
                                <td>
                                    {% if analysis.request.mode.value == 'advanced' %}
                                        <span class="badge bg-info">Mode 2 (Advanced)</span>
                                    {% else %}
                                        <span class="badge bg-primary">Mode 1 (Default)</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Machine:</strong></td>
                                <td>{{ analysis.request.machine or 'First Available' }}</td>
                            </tr>
                            {% if analysis.result.cape_task_id %}
                            <tr>
                                <td><strong>CAPE Task ID:</strong></td>
                                <td>{{ analysis.result.cape_task_id }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Status Progress -->
                <div class="mb-4">
                    <h5>Progress</h5>
                    <div class="row">
                        <div class="col-md-8">
                            {% set status_value = analysis.result.status.value %}
                            <div class="progress mb-3" style="height: 25px;">
                                {% if status_value == 'pending' %}
                                    <div class="progress-bar bg-secondary" style="width: 25%">Pending</div>
                                {% elif status_value == 'running' %}
                                    <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated" style="width: 50%">Running</div>
                                {% elif status_value == 'processing' %}
                                    <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" style="width: 75%">Processing</div>
                                {% elif status_value == 'reported' %}
                                    <div class="progress-bar bg-success" style="width: 100%">Completed</div>
                                {% elif status_value == 'failed' %}
                                    <div class="progress-bar bg-danger" style="width: 100%">Failed</div>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex justify-content-between small text-muted">
                                <span class="{% if status_value in ['pending', 'running', 'processing', 'reported', 'failed'] %}fw-bold{% endif %}">Pending</span>
                                <span class="{% if status_value in ['running', 'processing', 'reported', 'failed'] %}fw-bold{% endif %}">Running</span>
                                <span class="{% if status_value in ['processing', 'reported', 'failed'] %}fw-bold{% endif %}">Processing</span>
                                <span class="{% if status_value in ['reported', 'failed'] %}fw-bold{% endif %}">Complete</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="status-{{ status_value }}">
                                    {% if status_value == 'pending' %}
                                        <i class="fas fa-clock"></i> Pending
                                    {% elif status_value == 'running' %}
                                        <i class="fas fa-play"></i> Running
                                    {% elif status_value == 'processing' %}
                                        <i class="fas fa-cog fa-spin"></i> Processing
                                    {% elif status_value == 'reported' %}
                                        <i class="fas fa-check-circle"></i> Completed
                                    {% elif status_value == 'failed' %}
                                        <i class="fas fa-times-circle"></i> Failed
                                    {% endif %}
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timestamps -->
                <div class="mb-4">
                    <h5>Timeline</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Created:</strong><br>
                            <small class="text-muted">{{ analysis.request.created_at.strftime('%Y-%m-%d %H:%M:%S') if analysis.request.created_at else 'N/A' }}</small>
                        </div>
                        {% if analysis.result.started_at %}
                        <div class="col-md-4">
                            <strong>Started:</strong><br>
                            <small class="text-muted">{{ analysis.result.started_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>
                        {% endif %}
                        {% if analysis.result.completed_at %}
                        <div class="col-md-4">
                            <strong>Completed:</strong><br>
                            <small class="text-muted">{{ analysis.result.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Error Message -->
                {% if analysis.result.error_message %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ analysis.result.error_message }}
                </div>
                {% endif %}

                <!-- Analysis Options -->
                {% if analysis.request.options %}
                <div class="mb-4">
                    <h5>Analysis Options</h5>
                    <div class="row">
                        {% for key, value in analysis.request.options.items() %}
                        <div class="col-md-6 col-lg-4 mb-2">
                            <span class="badge bg-light text-dark">{{ key }}={{ value }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    {% if status_value == 'reported' %}
                        <a href="{{ url_for('view_report', analysis_id=analysis.analysis_id) }}" class="btn btn-success">
                            <i class="fas fa-file-alt me-2"></i>View Report
                        </a>
                        <a href="{{ url_for('api_report', analysis_id=analysis.analysis_id) }}" class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-download me-2"></i>Download JSON
                        </a>
                    {% elif status_value in ['pending', 'running', 'processing'] %}
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Status
                        </button>
                    {% endif %}
                    
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-upload me-2"></i>Analyze Another File
                    </a>
                    
                    <a href="{{ url_for('list_analyses') }}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>All Analyses
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh for pending/running/processing analyses
document.addEventListener('DOMContentLoaded', function() {
    const status = '{{ analysis.result.status.value }}';
    
    if (['pending', 'running', 'processing'].includes(status)) {
        // Refresh every 10 seconds
        setTimeout(function() {
            location.reload();
        }, 10000);
    }
});
</script>
{% endblock %}

"""
Configuration for CAPE Malware Analyzer
"""

import os
from pathlib import Path

class Config:
    """Application configuration"""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'cape-malware-analyzer-secret-key-change-in-production'
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size
    
    # Server settings
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 8080))
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    # CAPE server settings
    CAPE_URL = os.environ.get('CAPE_URL', 'http://localhost:8000')
    CAPE_TIMEOUT = int(os.environ.get('CAPE_TIMEOUT', 300))  # Analysis timeout in seconds
    CAPE_MAX_WAIT = int(os.environ.get('CAPE_MAX_WAIT', 1800))  # Max wait time in seconds
    CAPE_POLL_INTERVAL = int(os.environ.get('CAPE_POLL_INTERVAL', 5))  # Status polling interval
    
    # File upload settings
    BASE_DIR = Path(__file__).parent
    UPLOAD_FOLDER = BASE_DIR / 'uploads'
    ALLOWED_EXTENSIONS = {
        'exe', 'dll', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        'zip', 'rar', '7z', 'tar', 'gz', 'apk', 'jar', 'bat', 'cmd', 'ps1',
        'vbs', 'js', 'html', 'htm', 'php', 'py', 'pl', 'rb', 'sh', 'bin',
        'com', 'scr', 'msi', 'dmp', 'pcap', 'cap'
    }
    
    # Analysis modes and options
    DEFAULT_OPTIONS = {
        'procmemdump': '1',
        'import_reconstruction': '1',
        'unpacker': '2',
        'norefer': '1',
        'no-iat': '1'
    }
    
    # Advanced analysis options (from web/submission/views.py)
    ADVANCED_OPTIONS = {
        # Core options
        'procmemdump': {
            'name': 'Process Memory Dump',
            'type': 'checkbox',
            'default': True,
            'description': 'Dump process memory for analysis'
        },
        'import_reconstruction': {
            'name': 'Import Reconstruction',
            'type': 'checkbox',
            'default': True,
            'description': 'Reconstruct import table'
        },
        'unpacker': {
            'name': 'Unpacker',
            'type': 'select',
            'options': {'0': 'Disabled', '1': 'Enabled', '2': 'Force'},
            'default': '2',
            'description': 'Automatic unpacking of packed executables'
        },
        'norefer': {
            'name': 'No Referrer',
            'type': 'checkbox',
            'default': True,
            'description': 'Disable HTTP referrer header'
        },
        'no-iat': {
            'name': 'No IAT',
            'type': 'checkbox',
            'default': True,
            'description': 'Disable Import Address Table reconstruction'
        },
        
        # Extended options
        'syscall': {
            'name': 'System Calls',
            'type': 'checkbox',
            'default': False,
            'description': 'Monitor system calls'
        },
        'kernel_analysis': {
            'name': 'Kernel Analysis',
            'type': 'checkbox',
            'default': False,
            'description': 'Enable kernel-level analysis'
        },
        'tor': {
            'name': 'Use Tor',
            'type': 'checkbox',
            'default': False,
            'description': 'Route traffic through Tor network'
        },
        'free': {
            'name': 'Free Analysis',
            'type': 'checkbox',
            'default': False,
            'description': 'Free analysis mode'
        },
        'nohuman': {
            'name': 'No Human Interaction',
            'type': 'checkbox',
            'default': False,
            'description': 'Disable human interaction simulation'
        },
        'mitmdump': {
            'name': 'MITM Dump',
            'type': 'checkbox',
            'default': False,
            'description': 'Enable MITM proxy dumping'
        },
        'unpack': {
            'name': 'Unpack',
            'type': 'checkbox',
            'default': False,
            'description': 'Force unpacking'
        },
        'curtain': {
            'name': 'Curtain',
            'type': 'checkbox',
            'default': False,
            'description': 'Enable Curtain analysis'
        },
        'digisig': {
            'name': 'Digital Signature',
            'type': 'checkbox',
            'default': False,
            'description': 'Analyze digital signatures'
        }
    }
    
    # Report fields to include (level 1 fields as specified in requirements)
    REPORT_FIELDS = [
        'statistics', 'target', 'CAPE', 'info', 'capa_summary', 'behavior',
        'debug', 'memory', 'network', 'procmon', 'url_analysis', 'procmemory',
        'signatures', 'malscore', 'ttps', 'malstatus', 'mitre_attck', 'shots'
    ]
    
    # Status mapping
    STATUS_MAPPING = {
        'pending': 'pending',
        'running': 'running',
        'processing': 'processing',
        'completed': 'processing',  # Map completed to processing until report is ready
        'reported': 'reported',
        'failed_analysis': 'failed',
        'failed_processing': 'failed',
        'failed_reporting': 'failed'
    }

# Create necessary directories
Config.UPLOAD_FOLDER.mkdir(exist_ok=True)

"""
CAPE Standalone Analyzer - Backend API Service
"""
import os
import threading
import logging
from datetime import datetime
from flask import Flask, request, jsonify, url_for
from flask_cors import CORS
from werkzeug.exceptions import RequestEntityTooLarge

from config import Config
from models import (
    Analysis, AnalysisRequest, AnalysisResult, AnalysisStorage,
    AnalysisStatus, AnalysisMode, FileInfo
)
from cape_client import CapeClient, CapeAPIError
from utils import (
    get_file_info, save_uploaded_file, cleanup_file, parse_analysis_options,
    create_success_response, create_error_response, validate_file_upload,
    setup_logging
)


# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)
Config.init_app(app)

# Setup CORS
CORS(app, origins=app.config['CORS_ORIGINS'])

# Setup logging
setup_logging(app.config['LOG_LEVEL'], app.config['LOG_FILE'])
logger = logging.getLogger(__name__)

# Global storage and locks
analysis_storage = AnalysisStorage()
analysis_lock = threading.Lock()

# Cape client
cape_client = CapeClient(
    cape_url=app.config['CAPE_URL'],
    timeout=app.config['CAPE_TIMEOUT'],
    max_wait=app.config['CAPE_MAX_WAIT'],
    verbose=app.config['DEBUG']
)


@app.errorhandler(RequestEntityTooLarge)
def handle_file_too_large(e):
    """Handle file too large error"""
    return jsonify(create_error_response(
        'File too large. Maximum size allowed is 100MB.',
        'FILE_TOO_LARGE'
    )), 413


@app.errorhandler(404)
def handle_not_found(e):
    """Handle 404 errors"""
    return jsonify(create_error_response('Endpoint not found', 'NOT_FOUND')), 404


@app.errorhandler(500)
def handle_internal_error(e):
    """Handle internal server errors"""
    logger.error(f"Internal server error: {e}")
    return jsonify(create_error_response(
        'Internal server error',
        'INTERNAL_ERROR'
    )), 500


# Health check endpoint
@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Test Cape connection
        cape_connected = cape_client.test_connection()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'cape_connected': cape_connected,
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }), 503


# Get available machines
@app.route('/api/v1/machines', methods=['GET'])
def get_machines():
    """Get list of available analysis machines"""
    try:
        machines = cape_client.get_machines()
        
        # Add "First Available" option
        machines_list = [{'name': 'first_available', 'label': 'First Available', 'platform': 'Auto'}]
        
        for machine in machines:
            machines_list.append({
                'name': machine.get('name', ''),
                'label': f"{machine.get('name', '')} ({machine.get('platform', 'Unknown')})",
                'platform': machine.get('platform', 'Unknown'),
                'status': machine.get('status', 'unknown')
            })
        
        return jsonify(create_success_response(machines_list))
        
    except Exception as e:
        logger.error(f"Error getting machines: {e}")
        return jsonify(create_error_response(f'Failed to get machines: {str(e)}')), 500


def analyze_file_async(analysis: Analysis):
    """Asynchronous file analysis function"""
    try:
        logger.info(f"Starting async analysis for {analysis.analysis_id}")
        
        # Update status to running
        with analysis_lock:
            analysis.result.update_status(AnalysisStatus.RUNNING, "Analysis started")
            analysis_storage.update(analysis)
        
        # Get file path
        file_path = os.path.join(
            app.config['UPLOAD_FOLDER'],
            f"{analysis.analysis_id}_{analysis.request.filename}"
        )
        
        if not os.path.exists(file_path):
            with analysis_lock:
                analysis.result.update_status(
                    AnalysisStatus.FAILED,
                    "File not found",
                    "Uploaded file was not found on disk"
                )
                analysis_storage.update(analysis)
            return
        
        # Check for existing analysis unless forced
        task_id = None
        was_cached = False
        
        if not analysis.request.force_reanalyze:
            existing_task_id = cape_client.check_existing_analysis(analysis.result.file_hash)
            if existing_task_id:
                task_id = existing_task_id
                was_cached = True
                logger.info(f"Using existing analysis: task {task_id}")
        
        # Submit new analysis if no existing one found
        if not task_id:
            try:
                task_id = cape_client.submit_file(
                    file_path=file_path,
                    options=analysis.request.options,
                    machine=analysis.request.machine if analysis.request.machine != 'first_available' else None
                )
                
                if not task_id:
                    with analysis_lock:
                        analysis.result.update_status(
                            AnalysisStatus.FAILED,
                            "Submission failed",
                            "Failed to submit file to Cape v2"
                        )
                        analysis_storage.update(analysis)
                    return
                
                logger.info(f"File submitted to Cape: task {task_id}")
                
            except CapeAPIError as e:
                with analysis_lock:
                    analysis.result.update_status(
                        AnalysisStatus.FAILED,
                        "Submission error",
                        str(e)
                    )
                    analysis_storage.update(analysis)
                return
        
        # Update with Cape task ID
        with analysis_lock:
            analysis.result.cape_task_id = task_id
            analysis.result.update_status(
                AnalysisStatus.RUNNING,
                f"Analysis running (Task {task_id})"
            )
            analysis_storage.update(analysis)
        
        # Start monitoring in background thread
        monitor_thread = threading.Thread(
            target=monitor_analysis_async,
            args=(analysis.analysis_id, task_id, was_cached)
        )
        monitor_thread.daemon = True
        monitor_thread.start()
        
    except Exception as e:
        logger.error(f"Error in async analysis: {e}")
        with analysis_lock:
            analysis.result.update_status(
                AnalysisStatus.FAILED,
                "Analysis error",
                str(e)
            )
            analysis_storage.update(analysis)
    
    finally:
        # Clean up uploaded file
        file_path = os.path.join(
            app.config['UPLOAD_FOLDER'],
            f"{analysis.analysis_id}_{analysis.request.filename}"
        )
        cleanup_file(file_path)


def monitor_analysis_async(analysis_id: str, task_id: int, was_cached: bool):
    """Monitor analysis progress asynchronously"""
    try:
        logger.info(f"Starting monitoring for analysis {analysis_id}, task {task_id}")
        
        if was_cached:
            # For cached results, try to get report immediately
            report = cape_client.get_report(task_id)
            if report:
                with analysis_lock:
                    analysis = analysis_storage.get(analysis_id)
                    if analysis:
                        analysis.result.report = report
                        analysis.result.update_status(
                            AnalysisStatus.REPORTED,
                            "Analysis completed (cached)"
                        )
                        analysis_storage.update(analysis)
                logger.info(f"Cached analysis completed: {analysis_id}")
                return
        
        # Poll for status updates
        poll_count = 0
        max_polls = app.config['MAX_STATUS_POLLS']
        poll_interval = app.config['STATUS_POLL_INTERVAL']
        
        while poll_count < max_polls:
            try:
                status, message = cape_client.get_task_status(task_id)
                
                with analysis_lock:
                    analysis = analysis_storage.get(analysis_id)
                    if not analysis:
                        logger.warning(f"Analysis {analysis_id} not found during monitoring")
                        return
                    
                    # Update status
                    analysis.result.update_status(status, message)
                    analysis_storage.update(analysis)
                
                # Check if analysis is complete
                if status == AnalysisStatus.REPORTED:
                    # Get the report
                    report = cape_client.get_report(task_id)
                    if report:
                        with analysis_lock:
                            analysis = analysis_storage.get(analysis_id)
                            if analysis:
                                analysis.result.report = report
                                analysis.result.update_status(
                                    AnalysisStatus.REPORTED,
                                    "Analysis completed successfully"
                                )
                                analysis_storage.update(analysis)
                        logger.info(f"Analysis completed successfully: {analysis_id}")
                    else:
                        with analysis_lock:
                            analysis = analysis_storage.get(analysis_id)
                            if analysis:
                                analysis.result.update_status(
                                    AnalysisStatus.FAILED,
                                    "Report not available",
                                    "Analysis completed but report could not be retrieved"
                                )
                                analysis_storage.update(analysis)
                        logger.error(f"Analysis completed but report not available: {analysis_id}")
                    return
                
                elif status == AnalysisStatus.FAILED:
                    logger.error(f"Analysis failed: {analysis_id} - {message}")
                    return
                
                # Wait before next poll
                import time
                time.sleep(poll_interval)
                poll_count += 1
                
            except Exception as e:
                logger.error(f"Error during monitoring poll: {e}")
                poll_count += 1
                import time
                time.sleep(poll_interval)
        
        # Timeout reached
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)
            if analysis and analysis.result.status not in [AnalysisStatus.REPORTED, AnalysisStatus.FAILED]:
                analysis.result.update_status(
                    AnalysisStatus.FAILED,
                    "Analysis timeout",
                    f"Analysis did not complete within {max_polls * poll_interval} seconds"
                )
                analysis_storage.update(analysis)
        
        logger.warning(f"Analysis monitoring timeout: {analysis_id}")

    except Exception as e:
        logger.error(f"Error in analysis monitoring: {e}")
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)
            if analysis:
                analysis.result.update_status(
                    AnalysisStatus.FAILED,
                    "Monitoring error",
                    str(e)
                )
                analysis_storage.update(analysis)


# File analysis endpoint
@app.route('/api/v1/analyze', methods=['POST'])
def analyze_file():
    """Submit file for analysis"""
    try:
        # Validate file upload
        if 'file' not in request.files:
            return jsonify(create_error_response('No file provided')), 400

        file = request.files['file']
        validation_error = validate_file_upload(
            file,
            app.config['ALLOWED_EXTENSIONS'],
            app.config['MAX_CONTENT_LENGTH']
        )

        if validation_error:
            return jsonify(create_error_response(validation_error)), 400

        # Get analysis parameters
        mode = request.form.get('mode', 'default')
        machine = request.form.get('machine', 'first_available')
        force_reanalyze = request.form.get('force_reanalyze', 'false').lower() == 'true'

        # Parse analysis options
        options = parse_analysis_options(request.form.to_dict(), mode)

        # Save uploaded file and get file info
        file_path, file_content = save_uploaded_file(
            file,
            app.config['UPLOAD_FOLDER'],
            ""  # Will be updated with analysis_id
        )

        # Get file information
        file_info = get_file_info(file, file_content)

        # Create analysis request
        analysis_request = AnalysisRequest(
            analysis_id="",  # Will be auto-generated
            filename=file_info.filename,
            file_info=file_info,
            mode=AnalysisMode(mode),
            options=options,
            machine=machine,
            force_reanalyze=force_reanalyze
        )

        # Create analysis object
        analysis = Analysis(
            analysis_id=analysis_request.analysis_id,
            request=analysis_request,
            result=AnalysisResult(analysis_id=analysis_request.analysis_id)
        )
        analysis.result.file_hash = file_info.sha256

        # Rename file with proper analysis ID
        old_file_path = file_path
        new_file_path = os.path.join(
            app.config['UPLOAD_FOLDER'],
            f"{analysis.analysis_id}_{file_info.filename}"
        )
        os.rename(old_file_path, new_file_path)

        # Store analysis
        with analysis_lock:
            analysis_storage.store(analysis)

        # Start analysis in background thread
        analysis_thread = threading.Thread(
            target=analyze_file_async,
            args=(analysis,)
        )
        analysis_thread.daemon = True
        analysis_thread.start()

        logger.info(f"Analysis started: {analysis.analysis_id}")

        return jsonify(create_success_response({
            'analysis_id': analysis.analysis_id,
            'message': 'File submitted for analysis',
            'status_url': url_for('get_analysis_status', analysis_id=analysis.analysis_id, _external=True),
            'report_url': url_for('get_analysis_report', analysis_id=analysis.analysis_id, _external=True)
        }))

    except Exception as e:
        logger.error(f"Error in analyze endpoint: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}')), 500


# Get analysis status
@app.route('/api/v1/status/<analysis_id>', methods=['GET'])
def get_analysis_status(analysis_id):
    """Get analysis status"""
    try:
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)

        if not analysis:
            return jsonify(create_error_response('Analysis not found')), 404

        return jsonify(create_success_response(analysis.to_status_response()))

    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}')), 500


# Get analysis report
@app.route('/api/v1/report/<analysis_id>', methods=['GET'])
def get_analysis_report(analysis_id):
    """Get analysis report"""
    try:
        with analysis_lock:
            analysis = analysis_storage.get(analysis_id)

        if not analysis:
            return jsonify(create_error_response('Analysis not found')), 404

        if analysis.status != AnalysisStatus.REPORTED:
            return jsonify(create_error_response('Analysis not completed')), 400

        return jsonify(create_success_response(analysis.to_report_response()))

    except Exception as e:
        logger.error(f"Error getting report: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}')), 500


# List all analyses
@app.route('/api/v1/analyses', methods=['GET'])
def list_analyses():
    """List all analyses"""
    try:
        with analysis_lock:
            analyses = analysis_storage.list_all()

        # Sort by creation time (newest first)
        analyses.sort(key=lambda x: x.request.created_at, reverse=True)

        # Convert to response format
        analyses_data = [analysis.to_status_response() for analysis in analyses]

        return jsonify(create_success_response({
            'analyses': analyses_data,
            'total': len(analyses_data)
        }))

    except Exception as e:
        logger.error(f"Error listing analyses: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}')), 500


# Get analysis configuration
@app.route('/api/v1/config', methods=['GET'])
def get_config():
    """Get analysis configuration options"""
    try:
        return jsonify(create_success_response({
            'analysis_modes': [
                {'value': 'default', 'label': 'Default (Recommended)'},
                {'value': 'advanced', 'label': 'Advanced (Custom Options)'}
            ],
            'default_options': app.config['DEFAULT_ANALYSIS_OPTIONS'],
            'advanced_options': app.config['ADVANCED_ANALYSIS_OPTIONS'],
            'allowed_extensions': list(app.config['ALLOWED_EXTENSIONS']),
            'max_file_size': app.config['MAX_CONTENT_LENGTH'],
            'cape_url': app.config['CAPE_URL']
        }))

    except Exception as e:
        logger.error(f"Error getting config: {e}")
        return jsonify(create_error_response(f'Internal server error: {str(e)}')), 500


if __name__ == '__main__':
    print("=" * 60)
    print("CAPE Standalone Analyzer - Backend API Service")
    print("=" * 60)
    print(f"Cape Server: {app.config['CAPE_URL']}")
    print(f"Upload folder: {app.config['UPLOAD_FOLDER']}")
    print(f"API server: http://{app.config['HOST']}:{app.config['PORT']}")
    print()
    print("API Endpoints:")
    print("  GET  /api/v1/health - Health check")
    print("  GET  /api/v1/machines - List available machines")
    print("  GET  /api/v1/config - Get configuration")
    print("  POST /api/v1/analyze - Submit file for analysis")
    print("  GET  /api/v1/status/<analysis_id> - Check analysis status")
    print("  GET  /api/v1/report/<analysis_id> - Get analysis report")
    print("  GET  /api/v1/analyses - List all analyses")
    print("=" * 60)

    try:
        app.run(
            host=app.config['HOST'],
            port=app.config['PORT'],
            debug=app.config['DEBUG'],
            threaded=True
        )
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error starting application: {e}")
        exit(1)

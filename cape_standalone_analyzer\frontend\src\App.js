import React, { useState } from 'react';
import axios from 'axios';

function App() {
  const [file, setFile] = useState(null);
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [analysisId, setAnalysisId] = useState('');
  const [dragOver, setDragOver] = useState(false);

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setStatus('');
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const droppedFile = e.dataTransfer.files[0];
    setFile(droppedFile);
    setStatus('');
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const testConnection = async () => {
    try {
      setStatus('🔄 Testing connection...');
      const response = await axios.get('/api/v1/health');
      setStatus(`🟢 Backend: ${response.data.status} - ${response.data.message}`);
    } catch (error) {
      setStatus(`🔴 Connection failed: ${error.message}`);
    }
  };

  const handleSubmit = async () => {
    if (!file) {
      setStatus('❌ Please select a file first');
      return;
    }

    setLoading(true);
    setStatus('📤 Uploading file...');

    const formData = new FormData();
    formData.append('file', file);
    formData.append('mode', 'default');

    try {
      const response = await axios.post('/api/v1/analyze', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        const id = response.data.data.analysis_id;
        setAnalysisId(id);
        setStatus(`✅ File uploaded! Analysis ID: ${id}`);
      } else {
        setStatus(`❌ Error: ${response.data.message}`);
      }
    } catch (error) {
      setStatus(`❌ Upload failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const checkStatus = async () => {
    if (!analysisId) return;
    
    try {
      const response = await axios.get(`/api/v1/status/${analysisId}`);
      if (response.data.success) {
        const statusData = response.data.data;
        setStatus(`📊 Status: ${statusData.status} - ${statusData.message}`);
      }
    } catch (error) {
      setStatus(`❌ Status check failed: ${error.message}`);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div style={{
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'
    }}>
      {/* Header */}
      <div style={{textAlign: 'center', marginBottom: '30px'}}>
        <h1 style={{
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontSize: '2.5em',
          marginBottom: '10px'
        }}>
          🛡️ CAPE Standalone Analyzer
        </h1>
        <p style={{color: '#666', fontSize: '18px'}}>Advanced Malware Analysis Platform</p>
        <small style={{color: '#999'}}>Node.js v20.19.4 | npm 10.8.2 | React 18.2.0</small>
      </div>

      {/* Connection Test */}
      <div style={{marginBottom: '20px'}}>
        <button 
          onClick={testConnection}
          style={{
            width: '100%',
            padding: '12px',
            background: 'linear-gradient(45deg, #007bff, #0056b3)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          🔍 Test Backend Connection
        </button>
      </div>

      {/* File Upload Area */}
      <div 
        style={{
          border: dragOver ? '3px dashed #28a745' : '3px dashed #ddd',
          borderRadius: '12px',
          padding: '40px',
          textAlign: 'center',
          margin: '20px 0',
          background: dragOver ? '#f0fff0' : '#fafafa',
          transition: 'all 0.3s ease'
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div style={{fontSize: '48px', marginBottom: '20px'}}>
          {dragOver ? '📥' : '📁'}
        </div>
        <input 
          type="file" 
          onChange={handleFileChange}
          style={{marginBottom: '15px', fontSize: '16px'}}
          accept=".exe,.dll,.pdf,.doc,.docx,.zip,.rar,.apk,.jar"
        />
        <p style={{fontSize: '18px', margin: '10px 0'}}>
          {dragOver ? 'Drop file here!' : 'Drag & drop a file here, or click to select'}
        </p>
        <p style={{fontSize: '14px', color: '#666'}}>
          Supported: .exe, .dll, .pdf, .doc, .zip, .rar, .apk, .jar and more
        </p>
        
        {file && (
          <div style={{
            marginTop: '20px',
            padding: '15px',
            background: '#e3f2fd',
            borderRadius: '8px',
            textAlign: 'left'
          }}>
            <h4>📄 Selected File:</h4>
            <div style={{fontFamily: 'monospace', fontSize: '14px'}}>
              <strong>Name:</strong> {file.name}<br/>
              <strong>Size:</strong> {formatFileSize(file.size)}<br/>
              <strong>Type:</strong> {file.type || 'Unknown'}<br/>
              <strong>Modified:</strong> {new Date(file.lastModified).toLocaleString()}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: analysisId ? '1fr 1fr' : '1fr',
        gap: '10px',
        marginBottom: '20px'
      }}>
        <button 
          onClick={handleSubmit} 
          disabled={!file || loading}
          style={{
            padding: '15px',
            background: loading ? '#28a745' : 'linear-gradient(45deg, #28a745, #20c997)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: !file || loading ? 'not-allowed' : 'pointer',
            opacity: !file || loading ? 0.6 : 1
          }}
        >
          {loading ? '⏳ Analyzing...' : '🚀 Submit for Analysis'}
        </button>
        
        {analysisId && (
          <button 
            onClick={checkStatus}
            style={{
              padding: '15px',
              background: 'linear-gradient(45deg, #ffc107, #fd7e14)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            🔄 Check Status
          </button>
        )}
      </div>

      {/* Status Display */}
      {status && (
        <div style={{
          margin: '20px 0',
          padding: '15px',
          background: '#f8f9fa',
          borderLeft: '5px solid #007bff',
          borderRadius: '8px',
          fontFamily: 'monospace'
        }}>
          <strong>📋 Status:</strong><br/>
          {status}
        </div>
      )}

      {/* API Info */}
      <div style={{marginTop: '40px', fontSize: '14px', color: '#666'}}>
        <h3>🔧 API Endpoints:</h3>
        <div style={{
          background: '#f8f9fa',
          padding: '15px',
          borderRadius: '8px',
          fontFamily: 'monospace'
        }}>
          <div>✅ <strong>GET</strong>  /api/v1/health - Health check</div>
          <div>📤 <strong>POST</strong> /api/v1/analyze - Submit file</div>
          <div>📊 <strong>GET</strong>  /api/v1/status/&lt;id&gt; - Check status</div>
        </div>
        
        <div style={{
          marginTop: '20px',
          textAlign: 'center',
          background: '#e3f2fd',
          padding: '15px',
          borderRadius: '8px'
        }}>
          <strong>🌐 Service URLs:</strong><br/>
          <a href="http://localhost:3000" target="_blank" rel="noopener noreferrer">
            Frontend: http://localhost:3000
          </a><br/>
          <a href="http://localhost:5000/api/v1/health" target="_blank" rel="noopener noreferrer">
            Backend API: http://localhost:5000/api/v1
          </a>
        </div>
      </div>
    </div>
  );
}

export default App;
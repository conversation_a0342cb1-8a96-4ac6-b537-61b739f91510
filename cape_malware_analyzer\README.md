# CAPE Malware Analyzer - Standalone Web Service

A standalone web service for malware and file analysis using the CAPE v2 API.

## Features

### Core Functionality
- **RESTful API** for file submission and analysis
- **Two Analysis Modes**:
  - **Mode 1 (Default)**: Simple file upload with default configurations
  - **Mode 2 (Advanced)**: Detailed analysis options and parameters
- **Machine Selection**: Support for "First Available" and specific VM selection
- **Status Management**: Proper status flow (pending → running → processing → reported)
- **Error Handling**: Robust handling of "still being analyzed" scenarios
- **Report Retrieval**: Full JSON reports with comprehensive field filtering

### API Endpoints

#### File Analysis
- `POST /api/v1/analyze` - Submit file for analysis
- `GET /api/v1/status/{analysis_id}` - Check analysis status
- `GET /api/v1/report/{analysis_id}` - Get analysis report
- `GET /api/v1/machines` - List available machines

#### Web Interface
- `GET /` - Main upload interface
- `GET /status/{analysis_id}` - Status page
- `GET /report/{analysis_id}` - Report viewer
- `GET /analyses` - List all analyses

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure CAPE URL**:
   ```bash
   export CAPE_URL="http://your-cape-server:8000"
   ```

3. **Run the Service**:
   ```bash
   python app.py
   ```

4. **Access the Service**:
   - Web Interface: http://localhost:8080
   - API Base URL: http://localhost:8080/api/v1

## Configuration

The service can be configured via environment variables:

- `CAPE_URL`: CAPE v2 server URL (default: http://localhost:8000)
- `CAPE_TIMEOUT`: Analysis timeout in seconds (default: 300)
- `CAPE_MAX_WAIT`: Maximum wait time in seconds (default: 1800)
- `PORT`: Service port (default: 8080)
- `SECRET_KEY`: Flask secret key for sessions

## API Usage Examples

### Mode 1 (Default) - Simple Analysis
```bash
curl -X POST -F "file=@malware.exe" \
     http://localhost:8080/api/v1/analyze
```

### Mode 2 (Advanced) - Custom Options
```bash
curl -X POST \
     -F "file=@malware.exe" \
     -F "mode=advanced" \
     -F "machine=Windows10" \
     -F "options=procmemdump=1,unpacker=2,syscall=1" \
     http://localhost:8080/api/v1/analyze
```

### Check Status
```bash
curl http://localhost:8080/api/v1/status/{analysis_id}
```

### Get Report
```bash
curl http://localhost:8080/api/v1/report/{analysis_id}
```

## Report Structure

The service returns comprehensive reports with the following level 1 fields:
- `statistics` - Analysis statistics
- `target` - Target file information
- `CAPE` - CAPE-specific analysis results
- `info` - General analysis information
- `capa_summary` - CAPA analysis summary
- `behavior` - Behavioral analysis
- `debug` - Debug information
- `memory` - Memory analysis
- `network` - Network activity
- `procmon` - Process monitoring
- `url_analysis` - URL analysis
- `procmemory` - Process memory dumps
- `signatures` - Detection signatures
- `malscore` - Malware score
- `ttps` - Tactics, Techniques, and Procedures
- `malstatus` - Malware status
- `mitre_attck` - MITRE ATT&CK mapping
- `shots` - Screenshots

Only fields containing actual data are included in the response.

## Status Flow

The service properly handles the CAPE analysis status flow:

1. **pending** - Task submitted and queued
2. **running** - Analysis in progress
3. **processing** - Results being processed
4. **reported** - Analysis complete and report available

The service automatically polls CAPE's API during the "processing" phase to detect when analysis is complete.

## Architecture

- **Standalone Application**: Runs independently of CAPE v2 environment
- **Asynchronous Processing**: Non-blocking file analysis
- **Robust Error Handling**: Comprehensive error management
- **Status Synchronization**: Real-time status updates from CAPE
- **Report Caching**: Efficient report retrieval and caching
- **Web Interface**: Optional user-friendly report viewer

## Development

### Project Structure
```
cape_malware_analyzer/
├── app.py              # Main Flask application
├── cape_client.py      # CAPE API client
├── config.py           # Configuration management
├── models.py           # Data models
├── utils.py            # Utility functions
├── templates/          # Web interface templates
├── static/             # Static assets
├── uploads/            # Temporary file storage
└── requirements.txt    # Python dependencies
```

### Testing
```bash
python -m pytest tests/
```

## License

This project is licensed under the MIT License.

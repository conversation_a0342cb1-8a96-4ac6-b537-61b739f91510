"""
Data models for CAPE Malware Analyzer
"""

import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum

class AnalysisStatus(Enum):
    """Analysis status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    PROCESSING = "processing"
    REPORTED = "reported"
    FAILED = "failed"

class AnalysisMode(Enum):
    """Analysis mode enumeration"""
    DEFAULT = "default"
    ADVANCED = "advanced"

@dataclass
class FileInfo:
    """File information"""
    name: str
    size: int
    md5: Optional[str] = None
    sha1: Optional[str] = None
    sha256: Optional[str] = None
    mime_type: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class AnalysisRequest:
    """Analysis request data"""
    analysis_id: str
    filename: str
    file_info: FileInfo
    mode: AnalysisMode
    options: Dict[str, str]
    machine: Optional[str] = None
    force_reanalyze: bool = False
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if not self.analysis_id:
            self.analysis_id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['mode'] = self.mode.value
        data['created_at'] = self.created_at.isoformat() if self.created_at else None
        return data

@dataclass
class AnalysisResult:
    """Analysis result data"""
    analysis_id: str
    status: AnalysisStatus
    cape_task_id: Optional[int] = None
    file_hash: Optional[str] = None
    was_cached: bool = False
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    report: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['status'] = self.status.value
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        return data

class Analysis:
    """Complete analysis object combining request and result"""
    
    def __init__(self, request: AnalysisRequest):
        self.request = request
        self.result = AnalysisResult(
            analysis_id=request.analysis_id,
            status=AnalysisStatus.PENDING
        )
    
    @property
    def analysis_id(self) -> str:
        return self.request.analysis_id
    
    @property
    def status(self) -> AnalysisStatus:
        return self.result.status
    
    @status.setter
    def status(self, value: AnalysisStatus):
        self.result.status = value
        if value == AnalysisStatus.RUNNING and not self.result.started_at:
            self.result.started_at = datetime.now()
        elif value in [AnalysisStatus.REPORTED, AnalysisStatus.FAILED] and not self.result.completed_at:
            self.result.completed_at = datetime.now()
    
    def update_result(self, **kwargs):
        """Update result fields"""
        for key, value in kwargs.items():
            if hasattr(self.result, key):
                setattr(self.result, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'analysis_id': self.analysis_id,
            'filename': self.request.filename,
            'file_info': self.request.file_info.to_dict(),
            'mode': self.request.mode.value,
            'options': self.request.options,
            'machine': self.request.machine,
            'force_reanalyze': self.request.force_reanalyze,
            'created_at': self.request.created_at.isoformat() if self.request.created_at else None,
            'status': self.result.status.value,
            'cape_task_id': self.result.cape_task_id,
            'file_hash': self.result.file_hash,
            'was_cached': self.result.was_cached,
            'started_at': self.result.started_at.isoformat() if self.result.started_at else None,
            'completed_at': self.result.completed_at.isoformat() if self.result.completed_at else None,
            'error_message': self.result.error_message,
            'has_report': self.result.report is not None
        }
    
    def to_status_response(self) -> Dict[str, Any]:
        """Convert to status API response format"""
        return {
            'error': False,
            'analysis_id': self.analysis_id,
            'status': self.result.status.value,
            'filename': self.request.filename,
            'created_at': self.request.created_at.isoformat() if self.request.created_at else None,
            'started_at': self.result.started_at.isoformat() if self.result.started_at else None,
            'completed_at': self.result.completed_at.isoformat() if self.result.completed_at else None,
            'cape_task_id': self.result.cape_task_id,
            'file_hash': self.result.file_hash,
            'was_cached': self.result.was_cached,
            'error_message': self.result.error_message
        }
    
    def to_report_response(self) -> Dict[str, Any]:
        """Convert to report API response format"""
        if self.result.status != AnalysisStatus.REPORTED or not self.result.report:
            return {
                'error': True,
                'message': 'Analysis not completed or report not available'
            }
        
        return {
            'error': False,
            'analysis_id': self.analysis_id,
            'cape_task_id': self.result.cape_task_id,
            'file_hash': self.result.file_hash,
            'was_cached': self.result.was_cached,
            'report': self.result.report
        }

class AnalysisStorage:
    """In-memory storage for analyses (in production, use database)"""
    
    def __init__(self):
        self._analyses: Dict[str, Analysis] = {}
    
    def store(self, analysis: Analysis):
        """Store analysis"""
        self._analyses[analysis.analysis_id] = analysis
    
    def get(self, analysis_id: str) -> Optional[Analysis]:
        """Get analysis by ID"""
        return self._analyses.get(analysis_id)
    
    def list_all(self) -> List[Analysis]:
        """Get all analyses"""
        return list(self._analyses.values())
    
    def list_by_status(self, status: AnalysisStatus) -> List[Analysis]:
        """Get analyses by status"""
        return [a for a in self._analyses.values() if a.status == status]
    
    def count(self) -> int:
        """Get total count of analyses"""
        return len(self._analyses)
    
    def count_by_status(self, status: AnalysisStatus) -> int:
        """Get count of analyses by status"""
        return len(self.list_by_status(status))

# Global storage instance
analysis_storage = AnalysisStorage()

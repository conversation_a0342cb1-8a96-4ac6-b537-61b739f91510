# Development Guide

This guide covers setting up a development environment and contributing to the CAPE Standalone Analyzer.

## Development Setup

### Prerequisites

- Python 3.8+
- Node.js 16+
- Docker (optional)
- Git
- Running CAPE v2 instance

### Backend Development

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd cape_standalone_analyzer/backend
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   # or
   venv\Scripts\activate     # Windows
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set environment variables**:
   ```bash
   export CAPE_URL=http://localhost:8000
   export FLASK_ENV=development
   export SECRET_KEY=dev-secret-key
   ```

5. **Run the backend**:
   ```bash
   python app.py
   ```

The backend will be available at http://localhost:5000

### Frontend Development

1. **Navigate to frontend directory**:
   ```bash
   cd cape_standalone_analyzer/frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set environment variables**:
   ```bash
   echo "REACT_APP_API_URL=http://localhost:5000/api/v1" > .env.local
   ```

4. **Start development server**:
   ```bash
   npm start
   ```

The frontend will be available at http://localhost:3000

### Full Stack Development

For full stack development, run both backend and frontend simultaneously:

```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate
python app.py

# Terminal 2 - Frontend
cd frontend
npm start
```

## Project Structure

```
cape_standalone_analyzer/
├── backend/                 # Flask backend
│   ├── app.py              # Main application
│   ├── config.py           # Configuration
│   ├── models.py           # Data models
│   ├── cape_client.py      # CAPE API client
│   ├── utils.py            # Utility functions
│   ├── requirements.txt    # Python dependencies
│   ├── Dockerfile          # Backend container
│   └── test_api.py         # API tests
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── App.js          # Main app component
│   │   └── index.js        # Entry point
│   ├── public/             # Static assets
│   ├── package.json        # Node dependencies
│   └── Dockerfile          # Frontend container
├── docs/                   # Documentation
├── docker-compose.yml      # Multi-container setup
├── nginx.conf              # Nginx configuration
├── start.sh               # Linux startup script
├── start.bat              # Windows startup script
└── README.md              # Main documentation
```

## Code Style and Standards

### Backend (Python)

- Follow PEP 8 style guide
- Use type hints where appropriate
- Document functions with docstrings
- Maximum line length: 100 characters

**Example**:
```python
def analyze_file(file_path: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Analyze a file using CAPE v2.
    
    Args:
        file_path: Path to the file to analyze
        options: Analysis options dictionary
        
    Returns:
        Analysis result dictionary
    """
    # Implementation here
    pass
```

### Frontend (JavaScript/React)

- Use ES6+ features
- Follow React best practices
- Use functional components with hooks
- Use meaningful component and variable names

**Example**:
```javascript
const AnalysisStatus = ({ analysisId }) => {
  const [status, setStatus] = useState('pending');
  
  useEffect(() => {
    // Fetch status logic
  }, [analysisId]);
  
  return (
    <div className="analysis-status">
      <StatusBadge status={status} />
    </div>
  );
};
```

## Testing

### Backend Testing

Run backend tests:
```bash
cd backend
python -m pytest tests/ -v
```

Create new tests in the `tests/` directory:
```python
import pytest
from app import app

@pytest.fixture
def client():
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def test_health_check(client):
    response = client.get('/api/v1/health')
    assert response.status_code == 200
    assert response.json['status'] == 'healthy'
```

### Frontend Testing

Run frontend tests:
```bash
cd frontend
npm test
```

Create component tests:
```javascript
import { render, screen } from '@testing-library/react';
import StatusBadge from '../components/StatusBadge';

test('renders status badge', () => {
  render(<StatusBadge status="pending" />);
  const badge = screen.getByText(/pending/i);
  expect(badge).toBeInTheDocument();
});
```

### Integration Testing

Test the complete API workflow:
```bash
cd backend
python test_api.py
```

## API Development

### Adding New Endpoints

1. **Define the route in `app.py`**:
   ```python
   @app.route('/api/v1/new-endpoint', methods=['POST'])
   def new_endpoint():
       try:
           # Implementation
           return jsonify(create_success_response(data))
       except Exception as e:
           return jsonify(create_error_response(str(e))), 500
   ```

2. **Add corresponding frontend service**:
   ```javascript
   // In services/api.js
   export const apiService = {
     // ... existing methods
     newEndpoint: (data) => api.post('/new-endpoint', data),
   };
   ```

3. **Update documentation**:
   - Add endpoint to `docs/api.md`
   - Include request/response examples

### Error Handling

Always use consistent error handling:

**Backend**:
```python
try:
    # Operation
    return jsonify(create_success_response(result))
except SpecificError as e:
    logger.error(f"Specific error: {e}")
    return jsonify(create_error_response(str(e), 'SPECIFIC_ERROR')), 400
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    return jsonify(create_error_response('Internal server error')), 500
```

**Frontend**:
```javascript
try {
  const response = await apiService.someOperation();
  // Handle success
} catch (error) {
  console.error('Operation failed:', error);
  toast.error(error.message || 'Operation failed');
}
```

## Database Development (Optional)

If implementing database support:

1. **Add SQLAlchemy models**:
   ```python
   from flask_sqlalchemy import SQLAlchemy
   
   db = SQLAlchemy(app)
   
   class Analysis(db.Model):
       id = db.Column(db.String(36), primary_key=True)
       filename = db.Column(db.String(255), nullable=False)
       status = db.Column(db.String(50), nullable=False)
       created_at = db.Column(db.DateTime, default=datetime.utcnow)
   ```

2. **Create migrations**:
   ```bash
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

## Frontend Component Development

### Creating New Components

1. **Create component file**:
   ```javascript
   // components/NewComponent.js
   import React from 'react';
   
   const NewComponent = ({ prop1, prop2 }) => {
     return (
       <div className="new-component">
         {/* Component content */}
       </div>
     );
   };
   
   export default NewComponent;
   ```

2. **Add styles** (if needed):
   ```css
   /* In App.css */
   .new-component {
     /* Styles here */
   }
   ```

3. **Export from index** (optional):
   ```javascript
   // components/index.js
   export { default as NewComponent } from './NewComponent';
   ```

### State Management

For complex state, consider using React Context:

```javascript
// contexts/AnalysisContext.js
import React, { createContext, useContext, useReducer } from 'react';

const AnalysisContext = createContext();

export const useAnalysis = () => {
  const context = useContext(AnalysisContext);
  if (!context) {
    throw new Error('useAnalysis must be used within AnalysisProvider');
  }
  return context;
};

export const AnalysisProvider = ({ children }) => {
  const [state, dispatch] = useReducer(analysisReducer, initialState);
  
  return (
    <AnalysisContext.Provider value={{ state, dispatch }}>
      {children}
    </AnalysisContext.Provider>
  );
};
```

## Debugging

### Backend Debugging

1. **Enable debug mode**:
   ```python
   app.run(debug=True)
   ```

2. **Use logging**:
   ```python
   import logging
   logger = logging.getLogger(__name__)
   logger.debug("Debug message")
   logger.info("Info message")
   logger.error("Error message")
   ```

3. **Use debugger**:
   ```python
   import pdb; pdb.set_trace()
   ```

### Frontend Debugging

1. **Browser DevTools**:
   - Console for JavaScript errors
   - Network tab for API calls
   - React DevTools extension

2. **React debugging**:
   ```javascript
   console.log('Debug data:', data);
   debugger; // Breakpoint
   ```

## Performance Optimization

### Backend Optimization

1. **Database queries** (if using database):
   - Use query optimization
   - Implement pagination
   - Add database indexes

2. **Caching**:
   ```python
   from flask_caching import Cache
   cache = Cache(app)
   
   @cache.memoize(timeout=300)
   def expensive_operation():
       # Cached operation
       pass
   ```

3. **Async operations**:
   ```python
   import asyncio
   import aiohttp
   
   async def async_cape_request():
       async with aiohttp.ClientSession() as session:
           async with session.get(url) as response:
               return await response.json()
   ```

### Frontend Optimization

1. **Code splitting**:
   ```javascript
   import { lazy, Suspense } from 'react';
   
   const LazyComponent = lazy(() => import('./LazyComponent'));
   
   function App() {
     return (
       <Suspense fallback={<div>Loading...</div>}>
         <LazyComponent />
       </Suspense>
     );
   }
   ```

2. **Memoization**:
   ```javascript
   import { memo, useMemo, useCallback } from 'react';
   
   const ExpensiveComponent = memo(({ data }) => {
     const processedData = useMemo(() => {
       return expensiveProcessing(data);
     }, [data]);
     
     return <div>{processedData}</div>;
   });
   ```

## Contributing

### Pull Request Process

1. **Fork the repository**
2. **Create a feature branch**:
   ```bash
   git checkout -b feature/new-feature
   ```
3. **Make changes and commit**:
   ```bash
   git commit -m "Add new feature"
   ```
4. **Push to your fork**:
   ```bash
   git push origin feature/new-feature
   ```
5. **Create a pull request**

### Commit Message Format

Use conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Examples:
- `feat(api): add new analysis endpoint`
- `fix(frontend): resolve file upload issue`
- `docs(readme): update installation instructions`

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Security considerations addressed
- [ ] Performance impact considered

## Deployment for Development

### Docker Development

Use Docker Compose for development:

```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  backend:
    build: ./backend
    volumes:
      - ./backend:/app
    environment:
      - FLASK_ENV=development
    ports:
      - "5000:5000"
  
  frontend:
    build: ./frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
```

Run with:
```bash
docker-compose -f docker-compose.dev.yml up
```

### Hot Reloading

Both backend and frontend support hot reloading in development mode:

- **Backend**: Flask automatically reloads on file changes
- **Frontend**: React development server reloads on file changes

## Troubleshooting Development Issues

### Common Backend Issues

1. **Import errors**: Check Python path and virtual environment
2. **CAPE connection**: Verify CAPE_URL and network connectivity
3. **Port conflicts**: Ensure port 5000 is available

### Common Frontend Issues

1. **Module not found**: Run `npm install`
2. **API connection**: Check REACT_APP_API_URL
3. **Build errors**: Clear node_modules and reinstall

### Docker Issues

1. **Build failures**: Check Dockerfile syntax
2. **Port conflicts**: Ensure ports are available
3. **Volume mounting**: Check file permissions

For additional help, check the main documentation or create an issue.

"""
Configuration module for CAPE Standalone Analyzer Backend
"""
import os
from datetime import timedelta


class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.environ.get('FLASK_ENV', 'production') == 'development'
    
    # Cape v2 Configuration
    CAPE_URL = os.environ.get('CAPE_URL', 'http://localhost:8000')
    CAPE_TIMEOUT = int(os.environ.get('CAPE_TIMEOUT', '300'))
    CAPE_MAX_WAIT = int(os.environ.get('CAPE_MAX_WAIT', '1800'))
    
    # Server Configuration
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('BACKEND_PORT', '5000'))
    
    # File Upload Configuration
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS = {
        'exe', 'dll', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        'zip', 'rar', '7z', 'tar', 'gz', 'apk', 'jar', 'bat', 'cmd', 'ps1',
        'vbs', 'js', 'html', 'htm', 'bin', 'com', 'scr', 'msi', 'dmp'
    }
    
    # Analysis Configuration
    DEFAULT_ANALYSIS_OPTIONS = {
        'procmemdump': '1',
        'import_reconstruction': '1',
        'unpacker': '2',
        'norefer': '1',
        'no-iat': '1'
    }
    
    ADVANCED_ANALYSIS_OPTIONS = {
        # Process Analysis
        'procmemdump': 'Process memory dump',
        'procdump': 'Process dump',
        'import_reconstruction': 'Import reconstruction',
        'unpacker': 'Unpacker level (0-2)',
        'syscall': 'System call monitoring',
        'kernel_analysis': 'Kernel analysis',
        
        # Behavioral Analysis
        'norefer': 'No referrer',
        'no-iat': 'No IAT reconstruction',
        'unpack': 'Unpacking',
        'free': 'Free analysis',
        'nohuman': 'No human interaction',
        
        # Network Analysis
        'tor': 'Use Tor',
        'mitmdump': 'MITM proxy dump',
        
        # Advanced Options
        'interactive': 'Interactive mode',
        'manual': 'Manual analysis',
        'timeout': 'Analysis timeout',
        'priority': 'Analysis priority',
        'machine': 'Target machine',
        'platform': 'Target platform',
        'package': 'Analysis package',
        'options': 'Custom options',
        'tags': 'Analysis tags',
        'custom': 'Custom parameters',
        'memory': 'Memory analysis',
        'enforce_timeout': 'Enforce timeout',
        'clock': 'System clock',
        'route': 'Network route'
    }
    
    # Status Configuration
    ANALYSIS_STATUSES = {
        'pending': 'Pending',
        'running': 'Running',
        'processing': 'Processing',
        'reported': 'Completed',
        'failed': 'Failed',
        'cancelled': 'Cancelled'
    }
    
    # Polling Configuration
    STATUS_POLL_INTERVAL = 5  # seconds
    MAX_STATUS_POLLS = 360    # 30 minutes at 5-second intervals
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'logs/cape_analyzer.log')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # CORS Configuration
    CORS_ORIGINS = ['http://localhost:3000', 'http://localhost:8080']
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Database Configuration (optional)
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///cape_analyzer.db')
    
    # Redis Configuration (optional)
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create upload directory if it doesn't exist
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        
        # Create logs directory if it doesn't exist
        log_dir = os.path.dirname(Config.LOG_FILE)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'INFO'


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    UPLOAD_FOLDER = '/tmp/cape_analyzer_test'
    DATABASE_URL = 'sqlite:///:memory:'


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

import React from 'react';
import { Clock, Play, Cog, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const StatusBadge = ({ status, showIcon = true, size = 'normal' }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          className: 'cape-status-pending',
          icon: Clock
        };
      case 'running':
        return {
          label: 'Running',
          className: 'cape-status-running',
          icon: Play
        };
      case 'processing':
        return {
          label: 'Processing',
          className: 'cape-status-processing',
          icon: Cog
        };
      case 'reported':
        return {
          label: 'Completed',
          className: 'cape-status-reported',
          icon: CheckCircle
        };
      case 'failed':
        return {
          label: 'Failed',
          className: 'cape-status-failed',
          icon: XCircle
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          className: 'cape-status-cancelled',
          icon: AlertCircle
        };
      default:
        return {
          label: 'Unknown',
          className: 'cape-status-cancelled',
          icon: AlertCircle
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;
  
  const sizeClasses = {
    small: 'text-xs px-2 py-0.5',
    normal: 'text-xs px-2.5 py-0.5',
    large: 'text-sm px-3 py-1'
  };

  const iconSizes = {
    small: 'h-3 w-3',
    normal: 'h-3 w-3',
    large: 'h-4 w-4'
  };

  return (
    <span className={`${config.className} ${sizeClasses[size]}`}>
      {showIcon && (
        <Icon className={`${iconSizes[size]} mr-1 inline`} />
      )}
      {config.label}
    </span>
  );
};

export default StatusBadge;

import React from 'react';
import { Settings, Info } from 'lucide-react';

const AnalysisOptions = ({ options, onChange, availableOptions = {} }) => {
  const handleOptionChange = (optionKey, value) => {
    onChange({
      ...options,
      [optionKey]: value
    });
  };

  const handleCheckboxChange = (optionKey, checked) => {
    onChange({
      ...options,
      [optionKey]: checked ? 'on' : ''
    });
  };

  // Group options by category
  const optionCategories = {
    'Process Analysis': [
      'process_memory',
      'process_dump', 
      'import_reconstruction',
      'unpacker',
      'syscall',
      'kernel_analysis'
    ],
    'Behavioral Analysis': [
      'norefer',
      'oldloader',
      'unpack',
      'free',
      'nohuman'
    ],
    'Network Analysis': [
      'tor',
      'mitmdump'
    ],
    'Advanced Options': [
      'interactive',
      'manual',
      'timeout',
      'priority'
    ]
  };

  const renderOption = (optionKey) => {
    const label = availableOptions[optionKey] || optionKey;
    const currentValue = options[optionKey] || '';

    // Determine option type based on key
    const isNumericOption = ['unpacker', 'timeout', 'priority'].includes(optionKey);
    const isCheckboxOption = !isNumericOption;

    return (
      <div key={optionKey} className="flex items-center justify-between py-2">
        <div className="flex items-center space-x-2">
          <label htmlFor={optionKey} className="text-sm font-medium text-gray-700">
            {label}
          </label>
          {optionKey === 'unpacker' && (
            <div className="group relative">
              <Info className="h-4 w-4 text-gray-400 cursor-help" />
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                0=Disabled, 1=Basic, 2=Advanced
              </div>
            </div>
          )}
        </div>

        {isCheckboxOption ? (
          <input
            type="checkbox"
            id={optionKey}
            checked={currentValue === 'on'}
            onChange={(e) => handleCheckboxChange(optionKey, e.target.checked)}
            className="cape-checkbox"
          />
        ) : (
          <input
            type="number"
            id={optionKey}
            value={currentValue}
            onChange={(e) => handleOptionChange(optionKey, e.target.value)}
            className="cape-input w-20"
            min="0"
            max={optionKey === 'unpacker' ? '2' : undefined}
            placeholder={optionKey === 'timeout' ? '300' : optionKey === 'priority' ? '1' : '0'}
          />
        )}
      </div>
    );
  };

  return (
    <div className="cape-card p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <Settings className="h-5 w-5 mr-2" />
        Advanced Analysis Options
      </h2>

      <div className="space-y-6">
        {Object.entries(optionCategories).map(([categoryName, categoryOptions]) => (
          <div key={categoryName}>
            <h3 className="text-lg font-medium text-gray-800 mb-3 border-b border-gray-200 pb-1">
              {categoryName}
            </h3>
            <div className="space-y-1">
              {categoryOptions
                .filter(optionKey => availableOptions[optionKey])
                .map(renderOption)
              }
            </div>
          </div>
        ))}

        {/* Custom Options */}
        <div>
          <h3 className="text-lg font-medium text-gray-800 mb-3 border-b border-gray-200 pb-1">
            Custom Options
          </h3>
          <div className="space-y-2">
            <label htmlFor="custom_options" className="cape-label">
              Custom Options String
            </label>
            <input
              type="text"
              id="custom_options"
              value={options.custom_options || ''}
              onChange={(e) => handleOptionChange('custom_options', e.target.value)}
              className="cape-input"
              placeholder="key1=value1,key2=value2"
            />
            <p className="text-xs text-gray-500">
              Enter custom options as comma-separated key=value pairs
            </p>
          </div>
        </div>
      </div>

      {/* Options Summary */}
      {Object.keys(options).length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Selected Options:</h4>
          <div className="text-sm text-gray-600">
            {Object.entries(options)
              .filter(([_, value]) => value && value !== '')
              .map(([key, value]) => (
                <span key={key} className="inline-block mr-3 mb-1">
                  <code className="bg-gray-200 px-2 py-1 rounded text-xs">
                    {key}={value}
                  </code>
                </span>
              ))
            }
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisOptions;

import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, File, X, AlertTriangle } from 'lucide-react';

const FileUpload = ({ onFileSelect, selectedFile, allowedExtensions = [], maxFileSize = 100 * 1024 * 1024 }) => {
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = useCallback((file) => {
    // Check file size
    if (file.size > maxFileSize) {
      return `File too large. Maximum size is ${formatFileSize(maxFileSize)}`;
    }

    // Check file extension if allowedExtensions is provided
    if (allowedExtensions.length > 0) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!allowedExtensions.includes(fileExtension)) {
        return `File type not allowed. Allowed types: ${allowedExtensions.join(', ')}`;
      }
    }

    return null;
  }, [allowedExtensions, maxFileSize]);

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      const error = rejection.errors[0];
      console.error('File rejected:', error.message);
      return;
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      const validationError = validateFile(file);
      
      if (validationError) {
        console.error('File validation failed:', validationError);
        return;
      }

      onFileSelect(file);
    }
  }, [onFileSelect, validateFile]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: allowedExtensions.length > 0 ? 
      Object.fromEntries(allowedExtensions.map(ext => [`.${ext}`, []])) : 
      undefined,
    maxSize: maxFileSize,
    multiple: false,
  });

  const removeFile = () => {
    onFileSelect(null);
  };

  return (
    <div className="space-y-4">
      {!selectedFile ? (
        <div
          {...getRootProps()}
          className={`dropzone ${isDragActive ? 'active' : ''} ${isDragReject ? 'reject' : ''}`}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center space-y-4">
            <Upload className={`h-12 w-12 ${isDragReject ? 'text-red-400' : 'text-gray-400'}`} />
            
            {isDragReject ? (
              <div className="text-center">
                <p className="text-red-600 font-medium">File not accepted</p>
                <p className="text-sm text-red-500">
                  Please check file type and size requirements
                </p>
              </div>
            ) : isDragActive ? (
              <div className="text-center">
                <p className="text-blue-600 font-medium">Drop the file here</p>
              </div>
            ) : (
              <div className="text-center">
                <p className="text-gray-600 font-medium">
                  Drag & drop a file here, or click to select
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  {allowedExtensions.length > 0 && (
                    <>Supported types: {allowedExtensions.slice(0, 8).join(', ')}
                    {allowedExtensions.length > 8 && ` and ${allowedExtensions.length - 8} more`}<br /></>
                  )}
                  Maximum size: {formatFileSize(maxFileSize)}
                </p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="cape-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <File className="h-8 w-8 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(selectedFile.size)} • {selectedFile.type || 'Unknown type'}
                </p>
              </div>
            </div>
            
            <button
              onClick={removeFile}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors"
              title="Remove file"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* File requirements info */}
      {allowedExtensions.length > 0 && (
        <div className="alert alert-info">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium mb-1">File Requirements</h4>
              <ul className="text-sm space-y-1">
                <li>• Maximum file size: {formatFileSize(maxFileSize)}</li>
                <li>• Allowed file types: {allowedExtensions.join(', ')}</li>
                <li>• Only one file can be analyzed at a time</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;

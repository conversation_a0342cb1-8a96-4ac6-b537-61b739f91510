{"name": "cape-standalone-analyzer-frontend", "version": "1.0.0", "description": "Frontend for CAPE Standalone Analyzer", "private": true, "engines": {"node": "20.19.4", "npm": "10.8.2"}, "dependencies": {"react": "18.3.1", "react-dom": "18.3.1", "react-scripts": "5.0.1", "axios": "1.7.7", "react-router-dom": "6.30.1", "react-hot-toast": "2.4.1", "lucide-react": "0.436.0", "react-dropzone": "14.2.9", "clsx": "2.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "6.4.8", "@testing-library/react": "16.0.1", "@testing-library/user-event": "14.5.2"}, "proxy": "http://localhost:5000"}
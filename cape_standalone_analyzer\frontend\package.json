{"name": "cape-standalone-analyzer-frontend", "version": "1.0.0", "description": "Frontend for CAPE Standalone Analyzer", "private": true, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "dependencies": {"@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "react-scripts": "5.0.1", "axios": "^1.7.7", "react-dropzone": "^14.2.9", "@tanstack/react-query": "^5.51.23", "react-hot-toast": "^2.4.1", "lucide-react": "^0.436.0", "clsx": "^2.1.1", "tailwindcss": "^3.4.10", "@headlessui/react": "^2.1.3", "date-fns": "^3.6.0", "react-syntax-highlighter": "^15.5.0", "react-json-view": "^1.21.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.20", "postcss": "^8.4.41", "prettier": "^3.3.3", "eslint": "^8.57.0"}, "proxy": "http://localhost:5000", "overrides": {"react-scripts": {"typescript": "^5.5.4"}}}
{"name": "cape-standalone-analyzer-frontend", "version": "1.0.0", "description": "Frontend for CAPE Standalone Analyzer", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "axios": "^1.5.1", "react-dropzone": "^14.2.3", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "lucide-react": "^0.284.0", "clsx": "^2.0.0", "tailwindcss": "^3.3.5", "@headlessui/react": "^1.7.17", "date-fns": "^2.30.0", "react-syntax-highlighter": "^15.5.0", "react-json-view": "^1.21.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "@types/react-syntax-highlighter": "^15.5.8", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}, "proxy": "http://localhost:5000"}
"""
Data models for CAPE Standalone Analyzer
"""
import uuid
import hashlib
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List


class AnalysisStatus(Enum):
    """Analysis status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    PROCESSING = "processing"
    REPORTED = "reported"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnalysisMode(Enum):
    """Analysis mode enumeration"""
    DEFAULT = "default"
    ADVANCED = "advanced"


@dataclass
class FileInfo:
    """File information model"""
    filename: str
    size: int
    md5: str = ""
    sha1: str = ""
    sha256: str = ""
    mime_type: str = ""
    
    def calculate_hashes(self, file_content: bytes):
        """Calculate file hashes"""
        self.md5 = hashlib.md5(file_content).hexdigest()
        self.sha1 = hashlib.sha1(file_content).hexdigest()
        self.sha256 = hashlib.sha256(file_content).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'filename': self.filename,
            'size': self.size,
            'md5': self.md5,
            'sha1': self.sha1,
            'sha256': self.sha256,
            'mime_type': self.mime_type
        }


@dataclass
class AnalysisRequest:
    """Analysis request model"""
    analysis_id: str
    filename: str
    file_info: FileInfo
    mode: AnalysisMode
    options: Dict[str, Any] = field(default_factory=dict)
    machine: str = "first_available"
    force_reanalyze: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization processing"""
        if not self.analysis_id:
            self.analysis_id = str(uuid.uuid4())
        
        if isinstance(self.mode, str):
            self.mode = AnalysisMode(self.mode)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'analysis_id': self.analysis_id,
            'filename': self.filename,
            'file_info': self.file_info.to_dict(),
            'mode': self.mode.value,
            'options': self.options,
            'machine': self.machine,
            'force_reanalyze': self.force_reanalyze,
            'created_at': self.created_at.isoformat()
        }


@dataclass
class AnalysisResult:
    """Analysis result model"""
    analysis_id: str
    status: AnalysisStatus = AnalysisStatus.PENDING
    cape_task_id: Optional[int] = None
    file_hash: str = ""
    message: str = ""
    error: Optional[str] = None
    report: Optional[Dict[str, Any]] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization processing"""
        if isinstance(self.status, str):
            self.status = AnalysisStatus(self.status)
    
    def update_status(self, status: AnalysisStatus, message: str = "", error: str = None):
        """Update analysis status"""
        self.status = status
        self.message = message
        if error:
            self.error = error
        self.updated_at = datetime.utcnow()
        
        if status == AnalysisStatus.RUNNING and not self.started_at:
            self.started_at = datetime.utcnow()
        elif status in [AnalysisStatus.REPORTED, AnalysisStatus.FAILED, AnalysisStatus.CANCELLED]:
            if not self.completed_at:
                self.completed_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'analysis_id': self.analysis_id,
            'status': self.status.value,
            'cape_task_id': self.cape_task_id,
            'file_hash': self.file_hash,
            'message': self.message,
            'error': self.error,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'updated_at': self.updated_at.isoformat()
        }
    
    def to_report_response(self) -> Dict[str, Any]:
        """Convert to report response format"""
        response = self.to_dict()
        if self.report:
            response['report'] = self.report
        return response


@dataclass
class Analysis:
    """Complete analysis model"""
    analysis_id: str
    request: AnalysisRequest
    result: AnalysisResult
    
    def __post_init__(self):
        """Post-initialization processing"""
        if not self.analysis_id:
            self.analysis_id = str(uuid.uuid4())
        
        # Ensure IDs match
        self.request.analysis_id = self.analysis_id
        self.result.analysis_id = self.analysis_id
    
    @property
    def status(self) -> AnalysisStatus:
        """Get current status"""
        return self.result.status
    
    @property
    def filename(self) -> str:
        """Get filename"""
        return self.request.filename
    
    @property
    def file_info(self) -> FileInfo:
        """Get file info"""
        return self.request.file_info
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'analysis_id': self.analysis_id,
            'request': self.request.to_dict(),
            'result': self.result.to_dict()
        }
    
    def to_status_response(self) -> Dict[str, Any]:
        """Convert to status response format"""
        return {
            'analysis_id': self.analysis_id,
            'filename': self.filename,
            'status': self.result.status.value,
            'message': self.result.message,
            'cape_task_id': self.result.cape_task_id,
            'file_info': self.file_info.to_dict(),
            'created_at': self.request.created_at.isoformat(),
            'started_at': self.result.started_at.isoformat() if self.result.started_at else None,
            'completed_at': self.result.completed_at.isoformat() if self.result.completed_at else None,
            'updated_at': self.result.updated_at.isoformat(),
            'error': self.result.error
        }
    
    def to_report_response(self) -> Dict[str, Any]:
        """Convert to report response format"""
        response = self.to_status_response()
        if self.result.report:
            response['report'] = self.result.report
        return response


class AnalysisStorage:
    """In-memory storage for analyses (replace with database in production)"""
    
    def __init__(self):
        self._analyses: Dict[str, Analysis] = {}
    
    def store(self, analysis: Analysis) -> None:
        """Store analysis"""
        self._analyses[analysis.analysis_id] = analysis
    
    def get(self, analysis_id: str) -> Optional[Analysis]:
        """Get analysis by ID"""
        return self._analyses.get(analysis_id)
    
    def get_by_hash(self, file_hash: str) -> Optional[Analysis]:
        """Get analysis by file hash"""
        for analysis in self._analyses.values():
            if analysis.result.file_hash == file_hash:
                return analysis
        return None
    
    def list_all(self) -> List[Analysis]:
        """List all analyses"""
        return list(self._analyses.values())
    
    def update(self, analysis: Analysis) -> None:
        """Update analysis"""
        self._analyses[analysis.analysis_id] = analysis
    
    def delete(self, analysis_id: str) -> bool:
        """Delete analysis"""
        if analysis_id in self._analyses:
            del self._analyses[analysis_id]
            return True
        return False
    
    def count(self) -> int:
        """Get total count"""
        return len(self._analyses)

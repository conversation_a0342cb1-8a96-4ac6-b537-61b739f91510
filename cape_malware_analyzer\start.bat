@echo off
echo ============================================================
echo CAPE Malware Analyzer - Standalone Web Service
echo ============================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install/upgrade dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

REM Set default environment variables if not set
if not defined CAPE_URL (
    set CAPE_URL=http://localhost:8000
)
if not defined PORT (
    set PORT=8080
)

echo.
echo Configuration:
echo   CAPE_URL: %CAPE_URL%
echo   PORT: %PORT%
echo.

REM Start the application
echo Starting CAPE Malware Analyzer...
python run.py

pause

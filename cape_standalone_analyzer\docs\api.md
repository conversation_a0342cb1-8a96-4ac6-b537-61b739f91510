# API Documentation

The CAPE Standalone Analyzer provides a RESTful API for programmatic access to malware analysis capabilities.

## Base URL

```
http://localhost:5000/api/v1
```

## Authentication

Currently, no authentication is required. In production deployments, consider implementing API key authentication or OAuth.

## Response Format

All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": true,
  "message": "Error description",
  "error_code": "ERROR_CODE" // Optional
}
```

## Endpoints

### Health Check

Check the health status of the service and its connection to CAPE v2.

**Endpoint**: `GET /health`

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "cape_connected": true,
  "version": "1.0.0"
}
```

### Get Available Machines

Retrieve a list of available analysis machines from CAPE v2.

**Endpoint**: `GET /machines`

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "name": "first_available",
      "label": "First Available",
      "platform": "Auto"
    },
    {
      "name": "Windows10",
      "label": "Windows10 (Windows)",
      "platform": "Windows",
      "status": "available"
    }
  ]
}
```

### Get Configuration

Retrieve analysis configuration options and settings.

**Endpoint**: `GET /config`

**Response**:
```json
{
  "success": true,
  "data": {
    "analysis_modes": [
      {"value": "default", "label": "Default (Recommended)"},
      {"value": "advanced", "label": "Advanced (Custom Options)"}
    ],
    "default_options": {
      "procmemdump": "1",
      "import_reconstruction": "1",
      "unpacker": "2",
      "norefer": "1",
      "no-iat": "1"
    },
    "advanced_options": {
      "procmemdump": "Process memory dump",
      "unpacker": "Unpacker level (0-2)",
      // ... more options
    },
    "allowed_extensions": ["exe", "dll", "pdf", "doc", "zip"],
    "max_file_size": 104857600,
    "cape_url": "http://localhost:8000"
  }
}
```

### Submit File for Analysis

Submit a file for malware analysis.

**Endpoint**: `POST /analyze`

**Content-Type**: `multipart/form-data`

**Parameters**:
- `file` (required): The file to analyze
- `mode` (optional): Analysis mode (`default` or `advanced`)
- `machine` (optional): Target machine name (default: `first_available`)
- `force_reanalyze` (optional): Force re-analysis (`true` or `false`)

**Advanced Mode Parameters** (when `mode=advanced`):
- `process_memory`: Enable process memory dump (`on` or empty)
- `unpacker`: Unpacker level (`0`, `1`, or `2`)
- `syscall`: Enable syscall monitoring (`on` or empty)
- `custom_options`: Custom options string (`key1=value1,key2=value2`)

**Example Request**:
```bash
curl -X POST \
  -F "file=@malware.exe" \
  -F "mode=default" \
  -F "machine=first_available" \
  http://localhost:5000/api/v1/analyze
```

**Response**:
```json
{
  "success": true,
  "message": "File submitted for analysis",
  "data": {
    "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
    "status_url": "http://localhost:5000/api/v1/status/550e8400-e29b-41d4-a716-446655440000",
    "report_url": "http://localhost:5000/api/v1/report/550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### Get Analysis Status

Check the status of a submitted analysis.

**Endpoint**: `GET /status/{analysis_id}`

**Response**:
```json
{
  "success": true,
  "data": {
    "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
    "filename": "malware.exe",
    "status": "running",
    "message": "Analysis running (Task 123)",
    "cape_task_id": 123,
    "file_info": {
      "filename": "malware.exe",
      "size": 1024000,
      "md5": "5d41402abc4b2a76b9719d911017c592",
      "sha1": "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d",
      "sha256": "2c26b46b68ffc68ff99b453c1d30413413422d706483bfa0f98a5e886266e7ae",
      "mime_type": "application/x-executable"
    },
    "created_at": "2024-01-15T10:00:00Z",
    "started_at": "2024-01-15T10:01:00Z",
    "completed_at": null,
    "updated_at": "2024-01-15T10:05:00Z",
    "error": null
  }
}
```

**Status Values**:
- `pending`: Analysis is queued
- `running`: Analysis is in progress
- `processing`: Analysis completed, processing results
- `reported`: Analysis completed successfully
- `failed`: Analysis failed
- `cancelled`: Analysis was cancelled

### Get Analysis Report

Retrieve the complete analysis report for a completed analysis.

**Endpoint**: `GET /report/{analysis_id}`

**Response**:
```json
{
  "success": true,
  "data": {
    "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
    "filename": "malware.exe",
    "status": "reported",
    "message": "Analysis completed successfully",
    "cape_task_id": 123,
    "file_info": {
      // File information
    },
    "created_at": "2024-01-15T10:00:00Z",
    "started_at": "2024-01-15T10:01:00Z",
    "completed_at": "2024-01-15T10:15:00Z",
    "updated_at": "2024-01-15T10:15:00Z",
    "report": {
      // Complete CAPE analysis report
      "info": {
        "id": 123,
        "started": "2024-01-15 10:01:00",
        "ended": "2024-01-15 10:15:00",
        "duration": 840
      },
      "target": {
        "file": {
          "name": "malware.exe",
          "size": 1024000,
          "md5": "5d41402abc4b2a76b9719d911017c592",
          "sha1": "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d",
          "sha256": "2c26b46b68ffc68ff99b453c1d30413413422d706483bfa0f98a5e886266e7ae"
        }
      },
      "signatures": [
        {
          "name": "suspicious_behavior",
          "description": "Exhibits suspicious behavior",
          "severity": "medium"
        }
      ],
      "behavior": {
        "processes": [
          {
            "process_name": "malware.exe",
            "pid": 1234
          }
        ]
      },
      "network": {
        "dns": [
          {"request": "malicious.com"}
        ],
        "http": [
          {
            "method": "GET",
            "uri": "/download",
            "host": "malicious.com"
          }
        ]
      },
      "malscore": 7.5
    }
  }
}
```

### List All Analyses

Retrieve a list of all submitted analyses.

**Endpoint**: `GET /analyses`

**Response**:
```json
{
  "success": true,
  "data": {
    "analyses": [
      {
        "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
        "filename": "malware.exe",
        "status": "reported",
        "message": "Analysis completed successfully",
        "cape_task_id": 123,
        "file_info": {
          // File information
        },
        "created_at": "2024-01-15T10:00:00Z",
        "started_at": "2024-01-15T10:01:00Z",
        "completed_at": "2024-01-15T10:15:00Z",
        "updated_at": "2024-01-15T10:15:00Z",
        "error": null
      }
    ],
    "total": 1
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `FILE_TOO_LARGE` | Uploaded file exceeds size limit |
| `INVALID_FILE_TYPE` | File type not allowed |
| `NOT_FOUND` | Resource not found |
| `INTERNAL_ERROR` | Internal server error |
| `CAPE_CONNECTION_ERROR` | Cannot connect to CAPE v2 |
| `ANALYSIS_FAILED` | Analysis failed in CAPE v2 |

## Rate Limiting

Currently, no rate limiting is implemented. For production use, consider implementing rate limiting based on IP address or API key.

## Examples

### Python Example

```python
import requests
import time

# Submit file for analysis
with open('malware.exe', 'rb') as f:
    response = requests.post(
        'http://localhost:5000/api/v1/analyze',
        files={'file': f},
        data={
            'mode': 'default',
            'machine': 'first_available'
        }
    )

if response.json()['success']:
    analysis_id = response.json()['data']['analysis_id']
    print(f"Analysis submitted: {analysis_id}")
    
    # Poll for completion
    while True:
        status_response = requests.get(
            f'http://localhost:5000/api/v1/status/{analysis_id}'
        )
        
        status = status_response.json()['data']['status']
        print(f"Status: {status}")
        
        if status in ['reported', 'failed', 'cancelled']:
            break
            
        time.sleep(5)
    
    # Get report if completed
    if status == 'reported':
        report_response = requests.get(
            f'http://localhost:5000/api/v1/report/{analysis_id}'
        )
        report = report_response.json()['data']['report']
        print(f"Malware score: {report.get('malscore', 0)}")
```

### JavaScript Example

```javascript
// Submit file for analysis
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('mode', 'default');

const response = await fetch('/api/v1/analyze', {
    method: 'POST',
    body: formData
});

const result = await response.json();
if (result.success) {
    const analysisId = result.data.analysis_id;
    
    // Poll for status updates
    const pollStatus = async () => {
        const statusResponse = await fetch(`/api/v1/status/${analysisId}`);
        const statusData = await statusResponse.json();
        
        if (statusData.data.status === 'reported') {
            // Get full report
            const reportResponse = await fetch(`/api/v1/report/${analysisId}`);
            const reportData = await reportResponse.json();
            console.log('Analysis complete:', reportData.data.report);
        } else if (statusData.data.status === 'failed') {
            console.error('Analysis failed:', statusData.data.error);
        } else {
            // Continue polling
            setTimeout(pollStatus, 5000);
        }
    };
    
    pollStatus();
}
```

## WebSocket Support (Future)

Future versions may include WebSocket support for real-time status updates:

```javascript
const ws = new WebSocket(`ws://localhost:5000/api/v1/status/${analysisId}/ws`);
ws.onmessage = (event) => {
    const statusUpdate = JSON.parse(event.data);
    console.log('Status update:', statusUpdate);
};
```

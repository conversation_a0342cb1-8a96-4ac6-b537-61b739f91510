import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { Upload, Settings, Zap, Shield, AlertCircle } from 'lucide-react';

import FileUpload from '../components/FileUpload';
import AnalysisOptions from '../components/AnalysisOptions';
import { apiService } from '../services/api';

const HomePage = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState(null);
  const [analysisMode, setAnalysisMode] = useState('default');
  const [selectedMachine, setSelectedMachine] = useState('first_available');
  const [advancedOptions, setAdvancedOptions] = useState({});
  const [forceReanalyze, setForceReanalyze] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch available machines
  const { data: machinesData, isLoading: machinesLoading } = useQuery(
    'machines',
    apiService.getMachines,
    {
      onError: (error) => {
        console.error('Failed to fetch machines:', error);
      }
    }
  );

  // Fetch configuration
  const { data: configData } = useQuery(
    'config',
    apiService.getConfig,
    {
      onError: (error) => {
        console.error('Failed to fetch config:', error);
      }
    }
  );

  const machines = machinesData?.data || [];
  const config = configData?.data || {};

  const handleFileSelect = (file) => {
    setSelectedFile(file);
  };

  const handleSubmit = async () => {
    if (!selectedFile) {
      toast.error('Please select a file to analyze');
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('mode', analysisMode);
      formData.append('machine', selectedMachine);
      formData.append('force_reanalyze', forceReanalyze.toString());

      // Add advanced options if in advanced mode
      if (analysisMode === 'advanced') {
        Object.entries(advancedOptions).forEach(([key, value]) => {
          if (value) {
            formData.append(key, value);
          }
        });
      }

      const response = await apiService.submitAnalysis(formData);
      
      if (response.success) {
        const analysisId = response.data.analysis_id;
        toast.success('File submitted for analysis!');
        navigate(`/analysis/${analysisId}`);
      } else {
        toast.error(response.message || 'Failed to submit file');
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast.error('Failed to submit file for analysis');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Submit File for Analysis
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Upload a file to analyze it using the CAPE v2 malware analysis platform. 
          Choose between default settings for quick analysis or advanced mode for custom options.
        </p>
      </div>

      {/* Analysis Mode Selection */}
      <div className="cape-card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          Analysis Mode
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Default Mode */}
          <div
            className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
              analysisMode === 'default'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setAnalysisMode('default')}
          >
            <div className="flex items-center mb-2">
              <input
                type="radio"
                name="analysisMode"
                value="default"
                checked={analysisMode === 'default'}
                onChange={() => setAnalysisMode('default')}
                className="mr-3"
              />
              <Zap className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Default Mode</h3>
            </div>
            <p className="text-sm text-gray-600 ml-8">
              Quick analysis with optimized settings. Recommended for most files.
            </p>
          </div>

          {/* Advanced Mode */}
          <div
            className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
              analysisMode === 'advanced'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setAnalysisMode('advanced')}
          >
            <div className="flex items-center mb-2">
              <input
                type="radio"
                name="analysisMode"
                value="advanced"
                checked={analysisMode === 'advanced'}
                onChange={() => setAnalysisMode('advanced')}
                className="mr-3"
              />
              <Shield className="h-5 w-5 text-purple-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Advanced Mode</h3>
            </div>
            <p className="text-sm text-gray-600 ml-8">
              Full control over analysis parameters and options.
            </p>
          </div>
        </div>
      </div>

      {/* File Upload */}
      <div className="cape-card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Upload className="h-5 w-5 mr-2" />
          File Upload
        </h2>
        
        <FileUpload
          onFileSelect={handleFileSelect}
          selectedFile={selectedFile}
          allowedExtensions={config.allowed_extensions}
          maxFileSize={config.max_file_size}
        />
      </div>

      {/* Machine Selection */}
      <div className="cape-card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Analysis Machine
        </h2>
        
        {machinesLoading ? (
          <div className="flex items-center justify-center py-4">
            <div className="spinner mr-2"></div>
            <span>Loading machines...</span>
          </div>
        ) : (
          <select
            value={selectedMachine}
            onChange={(e) => setSelectedMachine(e.target.value)}
            className="cape-select"
          >
            {machines.map((machine) => (
              <option key={machine.name} value={machine.name}>
                {machine.label}
              </option>
            ))}
          </select>
        )}
      </div>

      {/* Advanced Options */}
      {analysisMode === 'advanced' && (
        <AnalysisOptions
          options={advancedOptions}
          onChange={setAdvancedOptions}
          availableOptions={config.advanced_options}
        />
      )}

      {/* Additional Options */}
      <div className="cape-card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Additional Options
        </h2>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="forceReanalyze"
            checked={forceReanalyze}
            onChange={(e) => setForceReanalyze(e.target.checked)}
            className="cape-checkbox mr-3"
          />
          <label htmlFor="forceReanalyze" className="text-sm text-gray-700">
            Force re-analysis (ignore cached results)
          </label>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-center">
        <button
          onClick={handleSubmit}
          disabled={!selectedFile || isSubmitting}
          className={`cape-button-primary px-8 py-3 text-lg ${
            (!selectedFile || isSubmitting) ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isSubmitting ? (
            <>
              <div className="spinner mr-2"></div>
              Submitting...
            </>
          ) : (
            <>
              <Upload className="h-5 w-5 mr-2" />
              Submit for Analysis
            </>
          )}
        </button>
      </div>

      {/* Info Box */}
      <div className="alert alert-info">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 mr-3 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium mb-1">Analysis Information</h3>
            <ul className="text-sm space-y-1">
              <li>• Analysis typically takes 5-15 minutes depending on file complexity</li>
              <li>• You'll be redirected to the status page to monitor progress</li>
              <li>• Supported file types: {config.allowed_extensions?.slice(0, 5).join(', ')} and more</li>
              <li>• Maximum file size: {Math.round((config.max_file_size || 0) / (1024 * 1024))}MB</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;

#!/usr/bin/env python3
"""
CAPE Malware Analyzer - Production Runner
"""

import os
import sys
import logging
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app import app
from config import Config

def setup_logging():
    """Setup logging configuration"""
    log_level = logging.DEBUG if Config.DEBUG else logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('cape_analyzer.log')
        ]
    )

def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        import flask
        import requests
        import magic
        print("✓ All dependencies are available")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please install dependencies: pip install -r requirements.txt")
        return False

def check_cape_connection():
    """Check connection to CAPE server"""
    try:
        import requests
        response = requests.get(f"{Config.CAPE_URL}/apiv2/cuckoo/status/", timeout=10)
        if response.status_code == 200:
            print(f"✓ CAPE server is accessible at {Config.CAPE_URL}")
            return True
        else:
            print(f"⚠ CAPE server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠ Could not connect to CAPE server: {e}")
        print(f"  Make sure CAPE is running at {Config.CAPE_URL}")
        return False

def main():
    """Main entry point"""
    print("=" * 60)
    print("CAPE Malware Analyzer - Standalone Web Service")
    print("=" * 60)
    
    # Setup logging
    setup_logging()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check CAPE connection
    check_cape_connection()
    
    # Print configuration
    print(f"Configuration:")
    print(f"  CAPE Server: {Config.CAPE_URL}")
    print(f"  Host: {Config.HOST}")
    print(f"  Port: {Config.PORT}")
    print(f"  Debug: {Config.DEBUG}")
    print(f"  Upload folder: {Config.UPLOAD_FOLDER}")
    print(f"  Max file size: {Config.MAX_CONTENT_LENGTH // 1024 // 1024}MB")
    print()
    
    # Print URLs
    print(f"Service URLs:")
    print(f"  Web interface: http://{Config.HOST}:{Config.PORT}")
    print(f"  API base URL: http://{Config.HOST}:{Config.PORT}/api/v1")
    print()
    
    print(f"API Endpoints:")
    print(f"  POST /api/v1/analyze - Submit file for analysis")
    print(f"  GET /api/v1/status/<analysis_id> - Check analysis status")
    print(f"  GET /api/v1/report/<analysis_id> - Get analysis report")
    print(f"  GET /api/v1/machines - List available machines")
    print()
    
    print(f"Web Interface:")
    print(f"  GET / - Main upload interface")
    print(f"  GET /status/<analysis_id> - Status page")
    print(f"  GET /report/<analysis_id> - Report viewer")
    print(f"  GET /analyses - List all analyses")
    print("=" * 60)
    
    # Start the application
    try:
        app.run(
            host=Config.HOST,
            port=Config.PORT,
            debug=Config.DEBUG,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()

"""
CAPE API Client for Malware Analysis
"""

import requests
import time
import json
import os
import hashlib
import logging
from typing import Optional, Dict, List, Any
from pathlib import Path

from config import Config

logger = logging.getLogger(__name__)

class CAPEClient:
    """Client for interacting with CAPE v2 API"""
    
    def __init__(self, cape_url: str = None, timeout: int = None, max_wait: int = None):
        """
        Initialize CAPE client
        
        Args:
            cape_url: CAPE server URL
            timeout: Analysis timeout in seconds
            max_wait: Maximum wait time for analysis completion
        """
        self.cape_url = (cape_url or Config.CAPE_URL).rstrip('/')
        self.timeout = timeout or Config.CAPE_TIMEOUT
        self.max_wait = max_wait or Config.CAPE_MAX_WAIT
        self.session = requests.Session()
        
        # Set reasonable timeouts for HTTP requests
        self.session.timeout = 30
        
    def calculate_file_hash(self, file_path: Path, hash_type: str = 'md5') -> Optional[str]:
        """Calculate file hash"""
        try:
            hash_func = getattr(hashlib, hash_type)()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating {hash_type} hash: {e}")
            return None
    
    def get_machines(self) -> List[Dict[str, Any]]:
        """Get list of available machines"""
        url = f"{self.cape_url}/apiv2/machines/list/"
        
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    machines = result['data']
                    logger.info(f"Retrieved {len(machines)} machines")
                    return machines
            else:
                logger.warning(f"Failed to get machines: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"Error getting machines: {e}")
        
        return []
    
    def search_existing_analysis(self, file_hash: str) -> Optional[int]:
        """Search for existing analysis by file hash"""
        url = f"{self.cape_url}/apiv2/tasks/search/md5/{file_hash}/"
        
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    tasks = result['data']
                    if tasks:
                        # Check tasks from most recent to oldest
                        for task in sorted(tasks, key=lambda x: x.get('id', 0), reverse=True):
                            if task.get('status') in ['completed', 'reported']:
                                task_id = task['id']
                                logger.info(f"Found existing analysis: Task ID {task_id}")
                                
                                # Verify report is actually available
                                if self._check_report_availability(task_id):
                                    logger.info(f"Report confirmed available for Task ID {task_id}")
                                    return task_id
                                else:
                                    logger.info(f"Report not available for Task ID {task_id}, checking next...")
                                    continue
        except Exception as e:
            logger.warning(f"Could not search existing analysis: {e}")
        
        return None
    
    def _check_report_availability(self, task_id: int) -> bool:
        """Check if report is actually available for a task"""
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/json/"
        
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('error'):
                        error_value = str(result.get('error_value', '')).lower()
                        if 'still being analyzed' in error_value:
                            logger.debug(f"Report check: {result.get('error_value')}")
                            return False
                    # If no error or different error, consider it available
                    return not result.get('error', False)
                except json.JSONDecodeError:
                    # If can't parse JSON but status is 200, consider it available
                    return True
        except Exception as e:
            logger.debug(f"Error checking report availability: {e}")
        
        return False
    
    def submit_file(self, file_path: Path, options: Dict[str, str] = None, 
                   machine: str = None) -> Optional[int]:
        """
        Submit file for analysis
        
        Args:
            file_path: Path to file to analyze
            options: Analysis options dictionary
            machine: Machine name to use (None for first available)
            
        Returns:
            Task ID if successful, None otherwise
        """
        url = f"{self.cape_url}/apiv2/tasks/create/file/"
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (file_path.name, f)}
                data = {
                    'timeout': self.timeout,
                    'priority': 1
                }
                
                # Set machine if specified
                if machine and machine.lower() != 'first_available':
                    data['machine'] = machine
                    logger.info(f"Using machine: {machine}")
                else:
                    logger.info("Using first available machine")
                
                # Merge with default options
                merged_options = Config.DEFAULT_OPTIONS.copy()
                if options:
                    merged_options.update(options)
                
                # Handle analysis options
                analysis_options = []
                standard_options = ['machine', 'package', 'timeout', 'priority', 'memory', 'enforce_timeout']
                
                for key, value in merged_options.items():
                    if key in standard_options:
                        data[key] = value
                    else:
                        # Analysis-specific options go into options string
                        analysis_options.append(f"{key}={value}")
                
                # Combine analysis options into options string
                if analysis_options:
                    data['options'] = ','.join(analysis_options)
                    logger.info(f"Analysis options: {data['options']}")
                
                logger.info(f"Submitting file: {file_path.name}")
                response = self.session.post(url, files=files, data=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    if not result.get('error') and result.get('data', {}).get('task_ids'):
                        task_id = result['data']['task_ids'][0]
                        logger.info(f"File submitted successfully. Task ID: {task_id}")
                        return task_id
                    else:
                        logger.error(f"Submission failed: {result.get('error_value', 'Unknown error')}")
                else:
                    logger.error(f"HTTP Error {response.status_code}: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error submitting file: {e}")
        
        return None

    def wait_for_completion(self, task_id: int) -> bool:
        """
        Wait for analysis to complete

        Args:
            task_id: Task ID to monitor

        Returns:
            True if analysis completed successfully, False otherwise
        """
        logger.info(f"Waiting for analysis completion (max {self.max_wait}s)...")

        start_time = time.time()
        last_status = None
        check_count = 0

        while time.time() - start_time < self.max_wait:
            status = self.check_status(task_id)
            check_count += 1

            if status != last_status:
                logger.info(f"Status: {status} (check #{check_count})")
                last_status = status

            # Map status according to our flow
            mapped_status = Config.STATUS_MAPPING.get(status, status)

            if mapped_status == 'pending':
                logger.debug("Task is pending...")
            elif mapped_status == 'running':
                logger.debug("Analysis is running...")
            elif mapped_status == 'processing':
                logger.debug("Processing results...")
                # Continue waiting, don't return yet
            elif mapped_status == 'reported':
                logger.info("Analysis completed and reported!")
                return True
            elif mapped_status == 'failed':
                logger.error(f"Analysis failed with status: {status}")
                return False
            elif status is None:
                logger.warning(f"Could not get status (check #{check_count})")
                # Try alternative method to check if report is available
                if self._check_report_availability(task_id):
                    logger.info("Report is available, assuming analysis completed!")
                    return True

            time.sleep(Config.CAPE_POLL_INTERVAL)

        logger.warning("Timeout waiting for analysis completion")
        logger.info("Checking if report is available despite timeout...")

        # Final check - maybe the report is ready even if status check failed
        if self._check_report_availability(task_id):
            logger.info("Report found! Analysis appears to be complete.")
            return True

        return False

    def get_report(self, task_id: int, format: str = 'json') -> Optional[Dict[str, Any]]:
        """
        Get analysis report

        Args:
            task_id: Task ID
            format: Report format ('json', 'lite', 'htmlsummary')

        Returns:
            Report data if successful, None otherwise
        """
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{format}/"

        try:
            logger.info(f"Getting report from: {url}")
            response = self.session.get(url, timeout=30)
            logger.debug(f"Report response status: {response.status_code}")

            if response.status_code == 200:
                if format in ['json', 'lite']:
                    result = response.json()
                    # Check if the response contains an error about still being analyzed
                    if result.get('error'):
                        error_value = str(result.get('error_value', '')).lower()
                        logger.debug(f"Report contains error: {result.get('error_value')}")
                        if 'still being analyzed' in error_value:
                            logger.info("Task is still being analyzed!")
                            return None
                    return result
                else:
                    return {'raw_content': response.text}
            elif response.status_code == 404:
                logger.warning(f"{format.upper()} report not found, trying alternatives...")

                # Try alternative formats if requested format fails
                fallback_formats = ['json', 'lite'] if format != 'json' else ['lite']

                for fallback_format in fallback_formats:
                    fallback_url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{fallback_format}/"
                    try:
                        fallback_response = self.session.get(fallback_url, timeout=30)
                        if fallback_response.status_code == 200:
                            logger.info(f"Using {fallback_format.upper()} format instead")
                            result = fallback_response.json()
                            # Check for error in fallback response too
                            if result.get('error') and 'still being analyzed' in str(result.get('error_value', '')).lower():
                                logger.debug(f"Fallback report not ready yet: {result.get('error_value')}")
                                return None
                            return result
                    except Exception as e:
                        logger.debug(f"Fallback format {fallback_format} failed: {e}")
                        continue
            else:
                # Handle other HTTP status codes
                try:
                    error_response = response.json()
                    if error_response.get('error') and 'still being analyzed' in str(error_response.get('error_value', '')).lower():
                        logger.info(f"Report not ready (HTTP {response.status_code}): {error_response.get('error_value')}")
                        return None
                except json.JSONDecodeError:
                    pass
                logger.error(f"HTTP Error {response.status_code} when getting report")

        except Exception as e:
            logger.error(f"Error getting report: {e}")

        return None

    def filter_report_fields(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter report to include only specified fields with actual data

        Args:
            report: Full report data

        Returns:
            Filtered report with only non-empty fields
        """
        filtered_report = {}

        for field in Config.REPORT_FIELDS:
            if field in report:
                value = report[field]
                # Check if field has actual content
                if value is not None and value != {} and value != [] and value != "":
                    filtered_report[field] = value

        return filtered_report
    
    def check_status(self, task_id: int) -> Optional[str]:
        """Check task status"""
        url = f"{self.cape_url}/apiv2/tasks/view/{task_id}/"
        
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    status = result['data'].get('status')
                    logger.debug(f"Status check for task {task_id}: {status}")
                    return status
                else:
                    logger.warning(f"Status check failed - no data or error in response")
            else:
                logger.warning(f"Status check failed with HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"Error checking status: {e}")
        
        return None

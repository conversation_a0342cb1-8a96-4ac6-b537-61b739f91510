import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from 'react-query';
import { 
  FileText, 
  ArrowLeft, 
  Download, 
  Eye, 
  Code, 
  Shield,
  Activity,
  Network,
  AlertTriangle
} from 'lucide-react';
import ReactJsonView from 'react-json-view';

import { apiService } from '../services/api';
import StatusBadge from '../components/StatusBadge';

const ReportPage = () => {
  const { analysisId } = useParams();
  const [activeTab, setActiveTab] = useState('summary');

  const { data, isLoading, error } = useQuery(
    ['report', analysisId],
    () => apiService.getAnalysisReport(analysisId),
    {
      enabled: !!analysisId,
    }
  );

  const reportData = data?.data;
  const report = reportData?.report;

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const tabs = [
    { id: 'summary', label: 'Summary', icon: Eye },
    { id: 'signatures', label: 'Signatures', icon: Shield },
    { id: 'behavior', label: 'Behavior', icon: Activity },
    { id: 'network', label: 'Network', icon: Network },
    { id: 'raw', label: 'Raw JSON', icon: Code },
  ];

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <div className="spinner mr-3"></div>
          <span className="text-lg">Loading report...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="cape-card p-6">
          <div className="flex items-center text-red-600 mb-4">
            <AlertTriangle className="h-6 w-6 mr-2" />
            <h2 className="text-xl font-semibold">Error Loading Report</h2>
          </div>
          <p className="text-gray-700 mb-4">{error.message}</p>
          <Link to={`/analysis/${analysisId}`} className="cape-button-primary">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Analysis
          </Link>
        </div>
      </div>
    );
  }

  if (!reportData || !report) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="cape-card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Report Not Available</h2>
          <p className="text-gray-700 mb-4">
            The analysis report is not yet available or the analysis has not completed successfully.
          </p>
          <Link to={`/analysis/${analysisId}`} className="cape-button-primary">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Analysis
          </Link>
        </div>
      </div>
    );
  }

  const renderSummary = () => (
    <div className="space-y-6">
      {/* File Information */}
      <div className="cape-card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">File Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Filename</dt>
                <dd className="text-sm text-gray-900">{reportData.filename}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">File Size</dt>
                <dd className="text-sm text-gray-900">{formatFileSize(reportData.file_info?.size)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">File Type</dt>
                <dd className="text-sm text-gray-900">{report.target?.file?.type || 'Unknown'}</dd>
              </div>
            </dl>
          </div>
          <div>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">MD5</dt>
                <dd className="text-sm text-gray-900 font-mono">{reportData.file_info?.md5}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">SHA1</dt>
                <dd className="text-sm text-gray-900 font-mono">{reportData.file_info?.sha1}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">SHA256</dt>
                <dd className="text-sm text-gray-900 font-mono">{reportData.file_info?.sha256}</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Analysis Results */}
      <div className="cape-card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Analysis Results</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{report.malscore || 0}</div>
            <div className="text-sm text-gray-500">Malware Score</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{report.signatures?.length || 0}</div>
            <div className="text-sm text-gray-500">Signatures</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {report.behavior?.processes?.length || 0}
            </div>
            <div className="text-sm text-gray-500">Processes</div>
          </div>
        </div>
      </div>

      {/* Timeline */}
      <div className="cape-card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Analysis Timeline</h3>
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Started</dt>
            <dd className="text-sm text-gray-900">{formatDateTime(reportData.started_at)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Completed</dt>
            <dd className="text-sm text-gray-900">{formatDateTime(reportData.completed_at)}</dd>
          </div>
        </dl>
      </div>
    </div>
  );

  const renderSignatures = () => (
    <div className="cape-card p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Detection Signatures ({report.signatures?.length || 0})
      </h3>
      {report.signatures && report.signatures.length > 0 ? (
        <div className="space-y-3">
          {report.signatures.map((signature, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{signature.name}</h4>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  signature.severity === 'high' ? 'bg-red-100 text-red-800' :
                  signature.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {signature.severity || 'info'}
                </span>
              </div>
              <p className="text-sm text-gray-600">{signature.description}</p>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No signatures detected.</p>
      )}
    </div>
  );

  const renderBehavior = () => (
    <div className="cape-card p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Behavioral Analysis</h3>
      {report.behavior ? (
        <div className="space-y-4">
          {report.behavior.processes && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                Processes ({report.behavior.processes.length})
              </h4>
              <div className="space-y-2">
                {report.behavior.processes.slice(0, 10).map((process, index) => (
                  <div key={index} className="text-sm bg-gray-50 p-2 rounded">
                    <span className="font-mono">{process.process_name}</span>
                    {process.pid && <span className="text-gray-500 ml-2">(PID: {process.pid})</span>}
                  </div>
                ))}
                {report.behavior.processes.length > 10 && (
                  <p className="text-sm text-gray-500">
                    ... and {report.behavior.processes.length - 10} more processes
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-500">No behavioral data available.</p>
      )}
    </div>
  );

  const renderNetwork = () => (
    <div className="cape-card p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Network Activity</h3>
      {report.network ? (
        <div className="space-y-4">
          {report.network.dns && report.network.dns.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                DNS Requests ({report.network.dns.length})
              </h4>
              <div className="space-y-1">
                {report.network.dns.slice(0, 10).map((dns, index) => (
                  <div key={index} className="text-sm font-mono bg-gray-50 p-2 rounded">
                    {dns.request}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {report.network.http && report.network.http.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">
                HTTP Requests ({report.network.http.length})
              </h4>
              <div className="space-y-1">
                {report.network.http.slice(0, 5).map((http, index) => (
                  <div key={index} className="text-sm bg-gray-50 p-2 rounded">
                    <div className="font-mono">{http.method} {http.uri}</div>
                    {http.host && <div className="text-gray-500">Host: {http.host}</div>}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-500">No network activity detected.</p>
      )}
    </div>
  );

  const renderRawJson = () => (
    <div className="cape-card p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Raw JSON Report</h3>
      <div className="bg-gray-50 rounded-lg p-4 overflow-auto max-h-96">
        <ReactJsonView
          src={report}
          theme="rjv-default"
          collapsed={2}
          displayDataTypes={false}
          displayObjectSize={false}
          enableClipboard={true}
          name="report"
        />
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'summary':
        return renderSummary();
      case 'signatures':
        return renderSignatures();
      case 'behavior':
        return renderBehavior();
      case 'network':
        return renderNetwork();
      case 'raw':
        return renderRawJson();
      default:
        return renderSummary();
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <FileText className="h-6 w-6 mr-2" />
            Analysis Report
          </h1>
          <div className="flex items-center space-x-4 mt-1">
            <StatusBadge status={reportData.status} />
            <span className="text-gray-600">{reportData.filename}</span>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link to={`/analysis/${analysisId}`} className="cape-button-secondary">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Analysis
          </Link>
          <button className="cape-button-primary">
            <Download className="h-4 w-4 mr-2" />
            Download Report
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="fade-in">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default ReportPage;

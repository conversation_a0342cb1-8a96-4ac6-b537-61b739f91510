import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { BrowserRouter } from 'react-router-dom';
import App from './App';

// Mock the API service
jest.mock('./services/api', () => ({
  apiService: {
    healthCheck: jest.fn(),
    getMachines: jest.fn(),
    getConfig: jest.fn(),
  }
}));

// Create a test wrapper with required providers
const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

test('renders CAPE Standalone Analyzer header', () => {
  const TestWrapper = createTestWrapper();
  
  render(
    <TestWrapper>
      <App />
    </TestWrapper>
  );
  
  const headerElement = screen.getByText(/CAPE Standalone Analyzer/i);
  expect(headerElement).toBeInTheDocument();
});

test('renders navigation links', () => {
  const TestWrapper = createTestWrapper();
  
  render(
    <TestWrapper>
      <App />
    </TestWrapper>
  );
  
  const homeLink = screen.getByText(/Home/i);
  const analysesLink = screen.getByText(/Analyses/i);
  
  expect(homeLink).toBeInTheDocument();
  expect(analysesLink).toBeInTheDocument();
});

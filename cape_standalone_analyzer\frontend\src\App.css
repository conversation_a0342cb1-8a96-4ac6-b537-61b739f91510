@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for CAPE Standalone Analyzer */

.cape-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cape-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.cape-button-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.cape-button-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.cape-button-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.cape-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.cape-select {
  @apply block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.cape-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.cape-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.cape-status-pending {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
}

.cape-status-running {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

.cape-status-processing {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800;
}

.cape-status-reported {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

.cape-status-failed {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800;
}

.cape-status-cancelled {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
}

/* File upload dropzone styles */
.dropzone {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-colors duration-200;
}

.dropzone:hover {
  @apply border-blue-400 bg-blue-50;
}

.dropzone.active {
  @apply border-blue-500 bg-blue-100;
}

.dropzone.reject {
  @apply border-red-400 bg-red-50;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Progress bar */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar-fill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* Code syntax highlighting */
.code-block {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm;
}

/* JSON viewer customization */
.react-json-view {
  @apply bg-gray-50 p-4 rounded-lg border;
}

/* Table styles */
.cape-table {
  @apply min-w-full divide-y divide-gray-200;
}

.cape-table thead {
  @apply bg-gray-50;
}

.cape-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.cape-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.cape-table tbody tr:nth-child(even) {
  @apply bg-gray-50;
}

.cape-table tbody tr:hover {
  @apply bg-gray-100;
}

/* Alert styles */
.alert {
  @apply p-4 rounded-md;
}

.alert-info {
  @apply bg-blue-50 border border-blue-200 text-blue-800;
}

.alert-success {
  @apply bg-green-50 border border-green-200 text-green-800;
}

.alert-warning {
  @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
}

.alert-error {
  @apply bg-red-50 border border-red-200 text-red-800;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .cape-card {
    @apply mx-2;
  }
  
  .cape-table {
    @apply text-xs;
  }
  
  .cape-table th,
  .cape-table td {
    @apply px-3 py-2;
  }
}

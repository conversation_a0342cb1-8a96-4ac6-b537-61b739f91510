version: '3.8'

services:
  cape-analyzer:
    build: .
    ports:
      - "8080:8080"
    environment:
      - CAPE_URL=http://cape-server:8000  # Change to your CAPE server URL
      - CAPE_TIMEOUT=300
      - CAPE_MAX_WAIT=1800
      - PORT=8080
      - DEBUG=false
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - cape-server
    networks:
      - cape-network

  # Example CAPE server service (uncomment and configure if needed)
  # cape-server:
  #   image: cape-server:latest  # Replace with actual CAPE server image
  #   ports:
  #     - "8000:8000"
  #   volumes:
  #     - cape-data:/opt/CAPEv2
  #   networks:
  #     - cape-network

networks:
  cape-network:
    driver: bridge

volumes:
  cape-data:

#!/usr/bin/env python3
"""
Test script for CAPE Malware Analyzer API
"""

import os
import sys
import time
import json
import requests
from pathlib import Path

# Configuration
BASE_URL = os.environ.get('TEST_BASE_URL', 'http://localhost:8080')
API_BASE = f"{BASE_URL}/api/v1"

def create_test_file():
    """Create a test file for analysis"""
    test_content = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*"
    test_file = Path("test_eicar.txt")
    test_file.write_text(test_content)
    return test_file

def test_machines_endpoint():
    """Test the machines endpoint"""
    print("1️⃣ Testing machines endpoint...")
    
    try:
        response = requests.get(f"{API_BASE}/machines", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('error') and data.get('machines'):
                machines = data['machines']
                print(f"   ✅ Found {len(machines)} machines")
                for machine in machines[:3]:  # Show first 3
                    print(f"      - {machine.get('name')}: {machine.get('label')}")
                return True
            else:
                print(f"   ❌ Invalid response: {data}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return False

def test_file_submission_mode1():
    """Test file submission in Mode 1 (default)"""
    print("\n2️⃣ Testing file submission - Mode 1 (Default)...")
    
    test_file = create_test_file()
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test_eicar.txt', f)}
            data = {'mode': 'default'}
            
            response = requests.post(f"{API_BASE}/analyze", files=files, data=data, timeout=30)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    analysis_id = result.get('analysis_id')
                    print(f"   ✅ File submitted successfully")
                    print(f"   Analysis ID: {analysis_id}")
                    print(f"   CAPE Task ID: {result.get('cape_task_id')}")
                    print(f"   Was Cached: {result.get('was_cached')}")
                    return analysis_id
                else:
                    print(f"   ❌ Submission failed: {result.get('message')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    finally:
        # Clean up test file
        if test_file.exists():
            test_file.unlink()
    
    return None

def test_file_submission_mode2():
    """Test file submission in Mode 2 (advanced)"""
    print("\n3️⃣ Testing file submission - Mode 2 (Advanced)...")
    
    test_file = create_test_file()
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test_eicar_advanced.txt', f)}
            data = {
                'mode': 'advanced',
                'machine': 'first_available',
                'options': 'procmemdump=1,unpacker=2,syscall=1'
            }
            
            response = requests.post(f"{API_BASE}/analyze", files=files, data=data, timeout=30)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    analysis_id = result.get('analysis_id')
                    print(f"   ✅ File submitted successfully")
                    print(f"   Analysis ID: {analysis_id}")
                    print(f"   CAPE Task ID: {result.get('cape_task_id')}")
                    print(f"   Was Cached: {result.get('was_cached')}")
                    return analysis_id
                else:
                    print(f"   ❌ Submission failed: {result.get('message')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    finally:
        # Clean up test file
        if test_file.exists():
            test_file.unlink()
    
    return None

def test_status_monitoring(analysis_id):
    """Test status monitoring"""
    print(f"\n4️⃣ Testing status monitoring for {analysis_id}...")
    
    max_checks = 20  # Maximum number of status checks
    check_interval = 5  # Seconds between checks
    
    for i in range(max_checks):
        try:
            response = requests.get(f"{API_BASE}/status/{analysis_id}", timeout=10)
            print(f"   Check {i+1}: Status {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if not data.get('error'):
                    status = data.get('status')
                    print(f"   Status: {status}")
                    
                    if status == 'reported':
                        print(f"   ✅ Analysis completed!")
                        return True
                    elif status == 'failed':
                        print(f"   ❌ Analysis failed: {data.get('error_message')}")
                        return False
                    elif status in ['pending', 'running', 'processing']:
                        print(f"   ⏳ Analysis in progress...")
                        time.sleep(check_interval)
                        continue
                else:
                    print(f"   ❌ Status error: {data.get('message')}")
                    return False
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
    
    print(f"   ⏰ Timeout after {max_checks} checks")
    return False

def test_report_retrieval(analysis_id):
    """Test report retrieval"""
    print(f"\n5️⃣ Testing report retrieval for {analysis_id}...")
    
    try:
        response = requests.get(f"{API_BASE}/report/{analysis_id}", timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('error'):
                report = data.get('report')
                if report:
                    print(f"   ✅ Report retrieved successfully")
                    print(f"   Report fields: {list(report.keys())}")
                    
                    # Check for required fields
                    required_fields = ['info', 'target', 'signatures']
                    found_fields = [field for field in required_fields if field in report]
                    print(f"   Required fields found: {found_fields}")
                    
                    return True
                else:
                    print(f"   ❌ No report data")
            else:
                print(f"   ❌ Report error: {data.get('message')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return False

def test_web_interface():
    """Test web interface endpoints"""
    print(f"\n6️⃣ Testing web interface...")
    
    endpoints = [
        ('/', 'Main page'),
        ('/analyses', 'Analyses list')
    ]
    
    success_count = 0
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            print(f"   {description}: Status {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ {description} accessible")
                success_count += 1
            else:
                print(f"   ❌ {description} failed")
                
        except Exception as e:
            print(f"   ❌ {description} exception: {e}")
    
    return success_count == len(endpoints)

def main():
    """Main test function"""
    print("=" * 60)
    print("CAPE Malware Analyzer - API Test Suite")
    print("=" * 60)
    print(f"Testing against: {BASE_URL}")
    print()
    
    # Test results
    results = []
    
    # Test 1: Machines endpoint
    results.append(test_machines_endpoint())
    
    # Test 2: File submission Mode 1
    analysis_id_1 = test_file_submission_mode1()
    results.append(analysis_id_1 is not None)
    
    # Test 3: File submission Mode 2
    analysis_id_2 = test_file_submission_mode2()
    results.append(analysis_id_2 is not None)
    
    # Test 4: Status monitoring (use first analysis)
    if analysis_id_1:
        results.append(test_status_monitoring(analysis_id_1))
        
        # Test 5: Report retrieval
        results.append(test_report_retrieval(analysis_id_1))
    else:
        results.extend([False, False])
    
    # Test 6: Web interface
    results.append(test_web_interface())
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    test_names = [
        "Machines endpoint",
        "File submission (Mode 1)",
        "File submission (Mode 2)",
        "Status monitoring",
        "Report retrieval",
        "Web interface"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == '__main__':
    sys.exit(main())

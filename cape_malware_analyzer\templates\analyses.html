{% extends "base.html" %}

{% block title %}All Analyses - CAPE Malware Analyzer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    All Analyses ({{ analyses|length }})
                </h3>
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Analysis
                </a>
            </div>
            <div class="card-body">
                {% if analyses %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Status</th>
                                <th>Mode</th>
                                <th>Created</th>
                                <th>Duration</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for analysis in analyses %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ analysis.request.filename }}</strong>
                                        {% if analysis.result.file_hash %}
                                        <br><small class="text-muted file-hash">{{ analysis.result.file_hash[:16] }}...</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% set status_value = analysis.result.status.value %}
                                    {% if status_value == 'pending' %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                    {% elif status_value == 'running' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-play me-1"></i>Running
                                        </span>
                                    {% elif status_value == 'processing' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-cog me-1"></i>Processing
                                        </span>
                                    {% elif status_value == 'reported' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Completed
                                        </span>
                                    {% elif status_value == 'failed' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Failed
                                        </span>
                                    {% endif %}
                                    
                                    {% if analysis.result.was_cached %}
                                    <br><small class="badge bg-info mt-1">Cached</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if analysis.request.mode.value == 'advanced' %}
                                        <span class="badge bg-info">Advanced</span>
                                    {% else %}
                                        <span class="badge bg-primary">Default</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        {{ analysis.request.created_at.strftime('%Y-%m-%d') if analysis.request.created_at else 'N/A' }}
                                        <br><small class="text-muted">{{ analysis.request.created_at.strftime('%H:%M:%S') if analysis.request.created_at else '' }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if analysis.result.started_at and analysis.result.completed_at %}
                                        {% set duration = (analysis.result.completed_at - analysis.result.started_at).total_seconds() %}
                                        {{ "%.0f"|format(duration) }}s
                                    {% elif analysis.result.started_at %}
                                        {% set duration = (moment().datetime - analysis.result.started_at).total_seconds() %}
                                        {{ "%.0f"|format(duration) }}s (ongoing)
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('analysis_status', analysis_id=analysis.analysis_id) }}" 
                                           class="btn btn-outline-primary" title="View Status">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        {% if status_value == 'reported' %}
                                        <a href="{{ url_for('view_report', analysis_id=analysis.analysis_id) }}" 
                                           class="btn btn-outline-success" title="View Report">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <a href="{{ url_for('api_report', analysis_id=analysis.analysis_id) }}" 
                                           class="btn btn-outline-info" title="Download JSON" target="_blank">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No analyses found</h5>
                    <p class="text-muted">Upload a file to start your first analysis.</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload File
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Statistics Card -->
        {% if analyses %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% set status_counts = {} %}
                    {% for analysis in analyses %}
                        {% set status = analysis.result.status.value %}
                        {% set _ = status_counts.update({status: status_counts.get(status, 0) + 1}) %}
                    {% endfor %}
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ analyses|length }}</h4>
                            <small class="text-muted">Total Analyses</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">{{ status_counts.get('reported', 0) }}</h4>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ status_counts.get('processing', 0) + status_counts.get('running', 0) + status_counts.get('pending', 0) }}</h4>
                            <small class="text-muted">In Progress</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger">{{ status_counts.get('failed', 0) }}</h4>
                            <small class="text-muted">Failed</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh if there are pending/running analyses
document.addEventListener('DOMContentLoaded', function() {
    const hasActiveAnalyses = {{ 'true' if analyses and any(a.result.status.value in ['pending', 'running', 'processing'] for a in analyses) else 'false' }};
    
    if (hasActiveAnalyses) {
        // Refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    }
});
</script>
{% endblock %}

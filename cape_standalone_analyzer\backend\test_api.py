#!/usr/bin/env python3
"""
Test script for CAPE Standalone Analyzer API
"""
import os
import sys
import time
import requests
import tempfile
from typing import Dict, Any


# Configuration
BASE_URL = "http://localhost:5000/api/v1"
TEST_FILE_CONTENT = b"This is a test file for CAPE analysis"


def create_test_file() -> str:
    """Create a temporary test file"""
    with tempfile.NamedTemporaryFile(mode='wb', suffix='.exe', delete=False) as f:
        f.write(TEST_FILE_CONTENT)
        return f.name


def test_health_check() -> bool:
    """Test health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data.get('status')}")
            print(f"  Cape connected: {data.get('cape_connected')}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False


def test_machines_endpoint() -> bool:
    """Test machines endpoint"""
    print("\nTesting machines endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/machines", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                machines = data.get('data', [])
                print(f"✓ Machines endpoint passed: {len(machines)} machines available")
                for machine in machines[:3]:  # Show first 3
                    print(f"  - {machine.get('label')}")
                return True
            else:
                print(f"✗ Machines endpoint failed: {data.get('message')}")
                return False
        else:
            print(f"✗ Machines endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Machines endpoint error: {e}")
        return False


def test_config_endpoint() -> bool:
    """Test config endpoint"""
    print("\nTesting config endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/config", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                config = data.get('data', {})
                print(f"✓ Config endpoint passed")
                print(f"  Analysis modes: {len(config.get('analysis_modes', []))}")
                print(f"  Allowed extensions: {len(config.get('allowed_extensions', []))}")
                return True
            else:
                print(f"✗ Config endpoint failed: {data.get('message')}")
                return False
        else:
            print(f"✗ Config endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Config endpoint error: {e}")
        return False


def test_file_submission_default() -> str:
    """Test file submission with default mode"""
    print("\nTesting file submission (default mode)...")
    test_file = create_test_file()
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test.exe', f, 'application/octet-stream')}
            data = {
                'mode': 'default',
                'machine': 'first_available'
            }
            
            response = requests.post(f"{BASE_URL}/analyze", files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                analysis_id = result.get('data', {}).get('analysis_id')
                print(f"✓ File submission passed: {analysis_id}")
                return analysis_id
            else:
                print(f"✗ File submission failed: {result.get('message')}")
                return None
        else:
            print(f"✗ File submission failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ File submission error: {e}")
        return None
    finally:
        # Clean up test file
        try:
            os.unlink(test_file)
        except:
            pass


def test_file_submission_advanced() -> str:
    """Test file submission with advanced mode"""
    print("\nTesting file submission (advanced mode)...")
    test_file = create_test_file()
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test_advanced.exe', f, 'application/octet-stream')}
            data = {
                'mode': 'advanced',
                'machine': 'first_available',
                'process_memory': 'on',
                'unpacker': '2',
                'syscall': 'on'
            }
            
            response = requests.post(f"{BASE_URL}/analyze", files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                analysis_id = result.get('data', {}).get('analysis_id')
                print(f"✓ Advanced file submission passed: {analysis_id}")
                return analysis_id
            else:
                print(f"✗ Advanced file submission failed: {result.get('message')}")
                return None
        else:
            print(f"✗ Advanced file submission failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Advanced file submission error: {e}")
        return None
    finally:
        # Clean up test file
        try:
            os.unlink(test_file)
        except:
            pass


def test_status_monitoring(analysis_id: str) -> bool:
    """Test status monitoring"""
    print(f"\nTesting status monitoring for {analysis_id}...")
    
    try:
        for i in range(5):  # Check status 5 times
            response = requests.get(f"{BASE_URL}/status/{analysis_id}", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    status_data = result.get('data', {})
                    status = status_data.get('status')
                    message = status_data.get('message', '')
                    print(f"  Status check {i+1}: {status} - {message}")
                    
                    if status in ['reported', 'failed']:
                        print(f"✓ Status monitoring completed: {status}")
                        return status == 'reported'
                else:
                    print(f"✗ Status check failed: {result.get('message')}")
                    return False
            else:
                print(f"✗ Status check failed: {response.status_code}")
                return False
            
            time.sleep(2)  # Wait 2 seconds between checks
        
        print("✓ Status monitoring test completed (still in progress)")
        return True
        
    except Exception as e:
        print(f"✗ Status monitoring error: {e}")
        return False


def test_analyses_list() -> bool:
    """Test analyses list endpoint"""
    print("\nTesting analyses list...")
    try:
        response = requests.get(f"{BASE_URL}/analyses", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                analyses = data.get('data', {}).get('analyses', [])
                total = data.get('data', {}).get('total', 0)
                print(f"✓ Analyses list passed: {total} analyses found")
                return True
            else:
                print(f"✗ Analyses list failed: {data.get('message')}")
                return False
        else:
            print(f"✗ Analyses list failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Analyses list error: {e}")
        return False


def main():
    """Main test function"""
    print("=" * 60)
    print("CAPE Standalone Analyzer - API Test Suite")
    print("=" * 60)
    print(f"Testing against: {BASE_URL}")
    print()
    
    # Test results
    results = []
    
    # Test 1: Health check
    results.append(test_health_check())
    
    # Test 2: Machines endpoint
    results.append(test_machines_endpoint())
    
    # Test 3: Config endpoint
    results.append(test_config_endpoint())
    
    # Test 4: File submission (default mode)
    analysis_id_1 = test_file_submission_default()
    results.append(analysis_id_1 is not None)
    
    # Test 5: File submission (advanced mode)
    analysis_id_2 = test_file_submission_advanced()
    results.append(analysis_id_2 is not None)
    
    # Test 6: Status monitoring (use first analysis)
    if analysis_id_1:
        results.append(test_status_monitoring(analysis_id_1))
    else:
        results.append(False)
    
    # Test 7: Analyses list
    results.append(test_analyses_list())
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    test_names = [
        "Health Check",
        "Machines Endpoint",
        "Config Endpoint", 
        "File Submission (Default)",
        "File Submission (Advanced)",
        "Status Monitoring",
        "Analyses List"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{i+1:2d}. {name:<25} {status}")
    
    print("-" * 60)
    print(f"TOTAL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())

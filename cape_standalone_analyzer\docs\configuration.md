# Configuration Reference

This document provides a comprehensive reference for all configuration options available in the CAPE Standalone Analyzer.

## Environment Variables

All configuration is done through environment variables, which can be set in the `.env` file or through the system environment.

### Core Configuration

#### CAPE_URL
- **Description**: URL of the CAPE v2 server
- **Default**: `http://localhost:8000`
- **Example**: `https://cape.example.com`
- **Required**: Yes

#### CAPE_TIMEOUT
- **Description**: Analysis timeout in seconds
- **Default**: `300`
- **Range**: `60-3600`
- **Example**: `600`

#### CAPE_MAX_WAIT
- **Description**: Maximum time to wait for analysis completion in seconds
- **Default**: `1800`
- **Range**: `300-7200`
- **Example**: `3600`

### Backend Configuration

#### FLASK_ENV
- **Description**: Flask environment mode
- **Default**: `production`
- **Options**: `development`, `production`, `testing`
- **Example**: `production`

#### SECRET_KEY
- **Description**: Secret key for Flask sessions and security
- **Default**: `dev-secret-key-change-in-production`
- **Example**: `your-very-secure-random-secret-key`
- **Required**: Yes (change in production)

#### HOST
- **Description**: Backend server host
- **Default**: `0.0.0.0`
- **Example**: `127.0.0.1`

#### BACKEND_PORT
- **Description**: Backend server port
- **Default**: `5000`
- **Range**: `1024-65535`
- **Example**: `8080`

#### DEBUG
- **Description**: Enable debug mode
- **Default**: `False`
- **Options**: `True`, `False`
- **Example**: `False`

### Frontend Configuration

#### REACT_APP_API_URL
- **Description**: Backend API URL for frontend
- **Default**: `http://localhost:5000/api/v1`
- **Example**: `https://api.example.com/api/v1`
- **Required**: Yes

#### FRONTEND_PORT
- **Description**: Frontend development server port
- **Default**: `3000`
- **Range**: `1024-65535`
- **Example**: `3001`

### Service Configuration

#### SERVICE_PORT
- **Description**: Main service port (nginx proxy)
- **Default**: `8080`
- **Range**: `1024-65535`
- **Example**: `80`

### File Upload Configuration

#### MAX_CONTENT_LENGTH
- **Description**: Maximum file upload size in bytes
- **Default**: `104857600` (100MB)
- **Example**: `524288000` (500MB)

#### ALLOWED_EXTENSIONS
- **Description**: Comma-separated list of allowed file extensions
- **Default**: `exe,dll,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar,7z,tar,gz,apk,jar,bat,cmd,ps1,vbs,js,html,htm`
- **Example**: `exe,dll,pdf,zip`

#### UPLOAD_FOLDER
- **Description**: Directory for uploaded files
- **Default**: `uploads`
- **Example**: `/app/uploads`

#### UPLOAD_TIMEOUT
- **Description**: Upload timeout in seconds
- **Default**: `300`
- **Range**: `30-1800`
- **Example**: `600`

### Analysis Configuration

#### DEFAULT_ANALYSIS_OPTIONS
- **Description**: Default analysis options for CAPE
- **Format**: JSON object
- **Default**: 
  ```json
  {
    "procmemdump": "1",
    "import_reconstruction": "1",
    "unpacker": "2",
    "norefer": "1",
    "no-iat": "1"
  }
  ```

#### STATUS_POLL_INTERVAL
- **Description**: Status polling interval in seconds
- **Default**: `5`
- **Range**: `1-60`
- **Example**: `10`

#### MAX_STATUS_POLLS
- **Description**: Maximum number of status polls before timeout
- **Default**: `360`
- **Range**: `10-1000`
- **Example**: `720`

### Logging Configuration

#### LOG_LEVEL
- **Description**: Logging level
- **Default**: `INFO`
- **Options**: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
- **Example**: `DEBUG`

#### LOG_FILE
- **Description**: Log file path
- **Default**: `logs/cape_analyzer.log`
- **Example**: `/var/log/cape_analyzer.log`

#### LOG_FORMAT
- **Description**: Log message format
- **Default**: `%(asctime)s - %(name)s - %(levelname)s - %(message)s`
- **Example**: `[%(levelname)s] %(asctime)s: %(message)s`

### Security Configuration

#### CORS_ORIGINS
- **Description**: Allowed CORS origins
- **Default**: `['http://localhost:3000', 'http://localhost:8080']`
- **Example**: `['https://example.com', 'https://app.example.com']`

#### PERMANENT_SESSION_LIFETIME
- **Description**: Session lifetime in seconds
- **Default**: `86400` (24 hours)
- **Range**: `3600-604800`
- **Example**: `43200` (12 hours)

### Database Configuration (Optional)

#### DATABASE_URL
- **Description**: Database connection URL
- **Default**: `sqlite:///cape_analyzer.db`
- **Example**: `postgresql://user:pass@localhost:5432/cape_analyzer`

#### DATABASE_POOL_SIZE
- **Description**: Database connection pool size
- **Default**: `5`
- **Range**: `1-20`
- **Example**: `10`

#### DATABASE_POOL_TIMEOUT
- **Description**: Database connection timeout in seconds
- **Default**: `30`
- **Range**: `5-300`
- **Example**: `60`

### Redis Configuration (Optional)

#### REDIS_URL
- **Description**: Redis connection URL
- **Default**: `redis://localhost:6379/0`
- **Example**: `redis://redis:6379/1`

#### REDIS_TIMEOUT
- **Description**: Redis connection timeout in seconds
- **Default**: `5`
- **Range**: `1-30`
- **Example**: `10`

### Docker Configuration

#### DOCKER_REGISTRY
- **Description**: Docker registry for custom images
- **Default**: None
- **Example**: `registry.example.com`

#### DOCKER_TAG
- **Description**: Docker image tag
- **Default**: `latest`
- **Example**: `v1.0.0`

## Configuration Files

### .env File

The `.env` file is the primary configuration method:

```env
# CAPE Configuration
CAPE_URL=http://localhost:8000
CAPE_TIMEOUT=300
CAPE_MAX_WAIT=1800

# Backend Configuration
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
BACKEND_PORT=5000

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
FRONTEND_PORT=3000

# Service Configuration
SERVICE_PORT=8080

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=exe,dll,pdf,doc,zip

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/cape_analyzer.log
```

### Docker Compose Configuration

Override settings in `docker-compose.yml`:

```yaml
version: '3.8'
services:
  backend:
    environment:
      - CAPE_URL=${CAPE_URL}
      - SECRET_KEY=${SECRET_KEY}
      - LOG_LEVEL=${LOG_LEVEL}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
```

### Nginx Configuration

Customize `nginx.conf` for proxy settings:

```nginx
server {
    listen 80;
    client_max_body_size 500M;  # Match MAX_FILE_SIZE
    
    location /api/ {
        proxy_pass http://backend:5000;
        proxy_read_timeout 600s;  # Match UPLOAD_TIMEOUT
    }
}
```

## Environment-Specific Configurations

### Development

```env
FLASK_ENV=development
DEBUG=True
LOG_LEVEL=DEBUG
CAPE_URL=http://localhost:8000
SECRET_KEY=dev-secret-key
```

### Testing

```env
FLASK_ENV=testing
DEBUG=True
LOG_LEVEL=DEBUG
CAPE_URL=http://test-cape:8000
DATABASE_URL=sqlite:///:memory:
```

### Production

```env
FLASK_ENV=production
DEBUG=False
LOG_LEVEL=INFO
CAPE_URL=https://cape.production.com
SECRET_KEY=very-secure-production-key
DATABASE_URL=******************************/cape_analyzer
REDIS_URL=redis://redis:6379/0
```

## Configuration Validation

The application validates configuration on startup:

### Required Variables
- `CAPE_URL`
- `SECRET_KEY` (must be changed from default in production)

### Validation Rules
- `CAPE_TIMEOUT` must be between 60-3600 seconds
- `MAX_STATUS_POLLS` must be positive integer
- `LOG_LEVEL` must be valid logging level
- `ALLOWED_EXTENSIONS` must be comma-separated list

### Validation Errors

Common validation errors and solutions:

1. **Invalid CAPE_URL**: Ensure URL is properly formatted with protocol
2. **Default SECRET_KEY**: Change from default value in production
3. **Invalid LOG_LEVEL**: Use DEBUG, INFO, WARNING, ERROR, or CRITICAL
4. **Port conflicts**: Ensure ports are available and not in use

## Configuration Best Practices

### Security
1. **Change default SECRET_KEY** in production
2. **Use HTTPS** for CAPE_URL in production
3. **Restrict CORS_ORIGINS** to known domains
4. **Use strong database passwords**

### Performance
1. **Increase CAPE_TIMEOUT** for complex files
2. **Adjust MAX_STATUS_POLLS** based on analysis time
3. **Configure appropriate upload limits**
4. **Use Redis for session storage** in production

### Monitoring
1. **Set appropriate LOG_LEVEL** for environment
2. **Configure log rotation** for LOG_FILE
3. **Monitor disk space** for uploads and logs
4. **Set up health check endpoints**

### Scalability
1. **Use external database** for multiple instances
2. **Configure Redis** for shared sessions
3. **Use load balancer** for multiple backend instances
4. **Separate file storage** for uploads

## Troubleshooting Configuration

### Common Issues

1. **CAPE connection fails**:
   - Check CAPE_URL is correct
   - Verify network connectivity
   - Ensure CAPE v2 is running

2. **File uploads fail**:
   - Check MAX_CONTENT_LENGTH setting
   - Verify nginx client_max_body_size
   - Ensure upload directory exists

3. **Frontend can't connect to backend**:
   - Verify REACT_APP_API_URL is correct
   - Check CORS_ORIGINS includes frontend URL
   - Ensure backend is running

4. **Database connection fails**:
   - Verify DATABASE_URL format
   - Check database server is running
   - Ensure credentials are correct

### Configuration Testing

Test configuration with these commands:

```bash
# Test CAPE connection
curl -f "$CAPE_URL/apiv2/cuckoo/status/"

# Test backend health
curl -f "http://localhost:$BACKEND_PORT/api/v1/health"

# Test frontend
curl -f "http://localhost:$FRONTEND_PORT"

# Test main service
curl -f "http://localhost:$SERVICE_PORT/health"
```

For additional configuration help, see the deployment guide or create an issue.

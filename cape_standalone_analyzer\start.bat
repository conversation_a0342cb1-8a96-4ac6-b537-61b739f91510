@echo off
setlocal enabledelayedexpansion

REM CAPE Standalone Analyzer - Windows Startup Script

echo ================================================
echo     CAPE Standalone Analyzer - Startup
echo ================================================
echo.

REM Configuration defaults
set DEFAULT_CAPE_URL=http://localhost:8000
set DEFAULT_SERVICE_PORT=8080

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not installed or not in PATH
    echo Please install Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo Error: Docker Compose is not installed or not in PATH
    echo Please install Docker Compose and try again.
    pause
    exit /b 1
)

REM Handle command line arguments
if "%1"=="--help" goto :help
if "%1"=="-h" goto :help
if "%1"=="--config" goto :config
if "%1"=="--stop" goto :stop
if "%1"=="--restart" goto :restart
if "%1"=="--logs" goto :logs
if "%1"=="" goto :main
goto :unknown_option

:help
echo CAPE Standalone Analyzer Startup Script
echo.
echo Usage: %0 [options]
echo.
echo Options:
echo   --help, -h     Show this help message
echo   --config       Reconfigure settings
echo   --stop         Stop all services
echo   --restart      Restart all services
echo   --logs         Show service logs
echo.
pause
exit /b 0

:config
call :prompt_config
call :create_env_file
echo Configuration updated. Run '%0' to start services.
pause
exit /b 0

:stop
echo Stopping CAPE Standalone Analyzer...
docker-compose down
echo Services stopped.
pause
exit /b 0

:restart
echo Restarting CAPE Standalone Analyzer...
docker-compose restart
echo Services restarted.
pause
exit /b 0

:logs
docker-compose logs -f
exit /b 0

:unknown_option
echo Unknown option: %1
echo Use '%0 --help' for usage information.
pause
exit /b 1

:main
REM Check if .env file exists
if not exist .env (
    call :prompt_config
    call :create_env_file
) else (
    echo Using existing .env configuration
    for /f "tokens=1,2 delims==" %%a in (.env) do (
        if "%%a"=="SERVICE_PORT" set SERVICE_PORT=%%b
        if "%%a"=="CAPE_URL" set CAPE_URL=%%b
    )
    if "!SERVICE_PORT!"=="" set SERVICE_PORT=%DEFAULT_SERVICE_PORT%
)

REM Test CAPE connection
call :test_cape_connection

REM Start services
call :start_services

REM Show service information
call :show_service_info

pause
exit /b 0

:prompt_config
echo Configuration Setup
echo Please provide the following configuration:
echo.

REM CAPE URL
set /p CAPE_URL="CAPE v2 URL [%DEFAULT_CAPE_URL%]: "
if "!CAPE_URL!"=="" set CAPE_URL=%DEFAULT_CAPE_URL%

REM Service Port
set /p SERVICE_PORT="Service Port [%DEFAULT_SERVICE_PORT%]: "
if "!SERVICE_PORT!"=="" set SERVICE_PORT=%DEFAULT_SERVICE_PORT%

REM Generate secret key (simplified for Windows)
set SECRET_KEY=change-this-secret-key-in-production-%RANDOM%-%RANDOM%

echo.
echo Configuration Summary:
echo   CAPE URL: !CAPE_URL!
echo   Service Port: !SERVICE_PORT!
echo   Secret Key: [Generated]
echo.
goto :eof

:create_env_file
echo Creating environment configuration...

(
echo # CAPE Standalone Analyzer Configuration
echo CAPE_URL=!CAPE_URL!
echo CAPE_TIMEOUT=300
echo CAPE_MAX_WAIT=1800
echo.
echo # Backend Configuration
echo FLASK_ENV=production
echo SECRET_KEY=!SECRET_KEY!
echo BACKEND_PORT=5000
echo.
echo # Frontend Configuration
echo REACT_APP_API_URL=http://localhost:5000/api/v1
echo FRONTEND_PORT=3000
echo.
echo # Service Configuration
echo SERVICE_PORT=!SERVICE_PORT!
echo.
echo # Security
echo ALLOWED_EXTENSIONS=exe,dll,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar,7z,tar,gz,apk,jar,bat,cmd,ps1,vbs,js,html,htm
echo.
echo # Upload Limits
echo MAX_FILE_SIZE=100MB
echo UPLOAD_TIMEOUT=300
echo.
echo # Logging
echo LOG_LEVEL=INFO
echo LOG_FILE=logs/cape_analyzer.log
) > .env

echo Environment file created: .env
goto :eof

:test_cape_connection
echo Testing connection to CAPE v2...

curl -s --connect-timeout 5 "!CAPE_URL!/apiv2/cuckoo/status/" >nul 2>&1
if errorlevel 1 (
    echo Warning: Cannot connect to CAPE v2 at !CAPE_URL!
    echo Please ensure CAPE v2 is running and accessible
    echo.
    set /p continue_anyway="Continue anyway? (y/N): "
    if /i not "!continue_anyway!"=="y" (
        echo Startup cancelled.
        pause
        exit /b 1
    )
) else (
    echo CAPE v2 is accessible at !CAPE_URL!
)
goto :eof

:start_services
echo Starting CAPE Standalone Analyzer...
echo.

echo Building Docker images...
docker-compose build

echo Starting services...
docker-compose up -d

echo Waiting for services to start...
timeout /t 10 /nobreak >nul

echo Checking service health...

REM Check backend
curl -s "http://localhost:5000/api/v1/health" >nul 2>&1
if errorlevel 1 (
    echo Backend service is not responding
) else (
    echo Backend service is running
)

REM Check frontend
curl -s "http://localhost:3000" >nul 2>&1
if errorlevel 1 (
    echo Frontend service is not responding
) else (
    echo Frontend service is running
)

REM Check nginx proxy
curl -s "http://localhost:!SERVICE_PORT!/health" >nul 2>&1
if errorlevel 1 (
    echo Nginx proxy is not responding
) else (
    echo Nginx proxy is running
)
goto :eof

:show_service_info
echo.
echo ================================================
echo     CAPE Standalone Analyzer - Ready!
echo ================================================
echo.
echo Service URLs:
echo   Web Interface: http://localhost:!SERVICE_PORT!
echo   Backend API:   http://localhost:5000/api/v1
echo   Frontend:      http://localhost:3000
echo.
echo Management Commands:
echo   View logs:     docker-compose logs -f
echo   Stop services: docker-compose down
echo   Restart:       docker-compose restart
echo.
echo API Endpoints:
echo   Health Check:  GET  /api/v1/health
echo   Submit File:   POST /api/v1/analyze
echo   Get Status:    GET  /api/v1/status/{analysis_id}
echo   Get Report:    GET  /api/v1/report/{analysis_id}
echo   List Analyses: GET  /api/v1/analyses
echo.
echo Note: Make sure CAPE v2 is running at !CAPE_URL!
echo.
goto :eof

{% extends "base.html" %}

{% block title %}Upload File - CAPE Malware Analyzer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Submit File for Analysis
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                    <!-- File Upload -->
                    <div class="mb-4">
                        <label for="file" class="form-label">
                            <i class="fas fa-file me-1"></i>
                            Select File
                        </label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">
                            Maximum file size: 500MB. 
                            Supported formats: exe, dll, pdf, doc, docx, zip, apk, and more.
                        </div>
                    </div>

                    <!-- Analysis Mode -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-cogs me-1"></i>
                            Analysis Mode
                        </label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysis_mode" id="mode_default" value="default" checked>
                            <label class="form-check-label" for="mode_default">
                                <strong>Mode 1 (Default)</strong> - Quick analysis with standard options
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysis_mode" id="mode_advanced" value="advanced">
                            <label class="form-check-label" for="mode_advanced">
                                <strong>Mode 2 (Advanced)</strong> - Detailed analysis with custom options
                            </label>
                        </div>
                    </div>

                    <!-- Machine Selection -->
                    <div class="mb-4">
                        <label for="machine" class="form-label">
                            <i class="fas fa-server me-1"></i>
                            Virtual Machine
                        </label>
                        <select class="form-select" id="machine" name="machine">
                            {% for machine in machines %}
                                <option value="{{ machine.name }}" 
                                        {% if machine.name == 'first_available' %}selected{% endif %}>
                                    {{ machine.label }}
                                    {% if machine.platform %}({{ machine.platform }}){% endif %}
                                    {% if machine.locked %} - Locked{% endif %}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            Select "First Available" to automatically use the first available VM.
                        </div>
                    </div>

                    <!-- Advanced Options (Hidden by default) -->
                    <div id="advancedOptions" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    Advanced Analysis Options
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for option_key, option_config in advanced_options.items() %}
                                        <div class="col-md-6 mb-3">
                                            {% if option_config.type == 'checkbox' %}
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="{{ option_key }}" name="{{ option_key }}" value="1"
                                                           {% if option_config.default %}checked{% endif %}>
                                                    <label class="form-check-label" for="{{ option_key }}">
                                                        {{ option_config.name }}
                                                    </label>
                                                    {% if option_config.description %}
                                                        <div class="form-text">{{ option_config.description }}</div>
                                                    {% endif %}
                                                </div>
                                            {% elif option_config.type == 'select' %}
                                                <label for="{{ option_key }}" class="form-label">{{ option_config.name }}</label>
                                                <select class="form-select form-select-sm" id="{{ option_key }}" name="{{ option_key }}">
                                                    {% for value, label in option_config.options.items() %}
                                                        <option value="{{ value }}" 
                                                                {% if value == option_config.default %}selected{% endif %}>
                                                            {{ label }}
                                                        </option>
                                                    {% endfor %}
                                                </select>
                                                {% if option_config.description %}
                                                    <div class="form-text">{{ option_config.description }}</div>
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Options -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="force_reanalyze" name="force_reanalyze">
                            <label class="form-check-label" for="force_reanalyze">
                                Force Re-analysis
                            </label>
                            <div class="form-text">
                                Force new analysis even if file was previously analyzed.
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-play me-2"></i>
                            Start Analysis
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- API Usage Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-code me-2"></i>
                    API Usage
                </h5>
            </div>
            <div class="card-body">
                <p>You can also use the REST API to submit files programmatically:</p>
                
                <h6>Mode 1 (Default) Example:</h6>
                <pre><code>curl -X POST -F "file=@malware.exe" \
     {{ request.url_root }}api/v1/analyze</code></pre>
                
                <h6>Mode 2 (Advanced) Example:</h6>
                <pre><code>curl -X POST \
     -F "file=@malware.exe" \
     -F "mode=advanced" \
     -F "machine=Windows10" \
     -F "options=procmemdump=1,unpacker=2,syscall=1" \
     {{ request.url_root }}api/v1/analyze</code></pre>
                
                <p class="mb-0">
                    <a href="{{ url_for('api_machines') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>
                        View API Documentation
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modeRadios = document.querySelectorAll('input[name="analysis_mode"]');
    const advancedOptions = document.getElementById('advancedOptions');
    const submitBtn = document.getElementById('submitBtn');
    const uploadForm = document.getElementById('uploadForm');
    
    // Toggle advanced options visibility
    modeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'advanced') {
                advancedOptions.style.display = 'block';
            } else {
                advancedOptions.style.display = 'none';
            }
        });
    });
    
    // Handle form submission
    uploadForm.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}

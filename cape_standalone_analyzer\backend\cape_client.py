"""
Cape v2 API Client for CAPE Standalone Analyzer
"""
import os
import time
import logging
import requests
from typing import Dict, Any, Optional, List, Tuple
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from models import AnalysisStatus


logger = logging.getLogger(__name__)


class CapeAPIError(Exception):
    """Cape API specific error"""
    pass


class CapeClient:
    """Cape v2 API client"""
    
    def __init__(self, cape_url: str, timeout: int = 300, max_wait: int = 1800, verbose: bool = False):
        """
        Initialize Cape client
        
        Args:
            cape_url: Base URL of Cape v2 web interface
            timeout: Analysis timeout in seconds
            max_wait: Maximum time to wait for completion
            verbose: Enable verbose logging
        """
        self.cape_url = cape_url.rstrip('/')
        self.timeout = timeout
        self.max_wait = max_wait
        self.verbose = verbose
        
        # Configure session with retries
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Default analysis options
        self.default_options = {
            'procmemdump': '1',
            'import_reconstruction': '1',
            'unpacker': '2',
            'norefer': '1',
            'no-iat': '1'
        }
    
    def _log(self, message: str, level: str = 'info'):
        """Log message if verbose mode is enabled"""
        if self.verbose:
            getattr(logger, level)(message)
    
    def test_connection(self) -> bool:
        """Test connection to Cape v2"""
        try:
            url = f"{self.cape_url}/apiv2/cuckoo/status/"
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                self._log(f"Cape connection successful: {response.json()}")
                return True
            else:
                self._log(f"Cape connection failed: HTTP {response.status_code}")
                return False
        except Exception as e:
            self._log(f"Connection test failed: {e}", 'error')
            return False
    
    def get_machines(self) -> List[Dict[str, Any]]:
        """Get list of available machines"""
        url = f"{self.cape_url}/apiv2/cuckoo/machines/"
        
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    machines = result['data']
                    self._log(f"Available machines: {[m.get('name') for m in machines]}")
                    return machines
            return []
        except Exception as e:
            self._log(f"Error getting machines: {e}", 'error')
            return []
    
    def check_existing_analysis(self, file_hash: str) -> Optional[int]:
        """Check if file has been analyzed before"""
        url = f"{self.cape_url}/apiv2/tasks/search/hash/{file_hash}/"

        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                result = response.json()
                self._log(f"Hash search response: {result}")  # Debug log

                if not result.get('error') and result.get('data'):
                    tasks = result['data']

                    # Handle different response formats
                    if isinstance(tasks, dict):
                        # If data is a dict, it might contain a 'tasks' key
                        tasks = tasks.get('tasks', [])
                    elif not isinstance(tasks, list):
                        # If not a list, convert to list
                        tasks = [tasks] if tasks else []

                    if tasks:
                        # Return the most recent completed task
                        for task in sorted(tasks, key=lambda x: x.get('id', 0), reverse=True):
                            if task.get('status') == 'reported':
                                self._log(f"Found existing analysis: task {task['id']}")
                                return task['id']
                else:
                    self._log(f"No existing analysis found for hash {file_hash}")
            else:
                self._log(f"Hash search failed: HTTP {response.status_code}")
            return None
        except Exception as e:
            self._log(f"Error checking existing analysis: {e}", 'error')
            return None
    
    def submit_file(self, file_path: str, options: Dict[str, Any] = None, machine: str = None) -> Optional[int]:
        """Submit file for analysis"""
        url = f"{self.cape_url}/apiv2/tasks/create/file/"
        
        # Prepare options
        analysis_options = self.default_options.copy()
        if options:
            analysis_options.update(options)
        
        # Convert options to string format
        options_str = ','.join([f"{k}={v}" for k, v in analysis_options.items() if v])
        
        # Prepare form data
        data = {
            'timeout': self.timeout,
            'priority': 1,
            'options': options_str
        }
        
        if machine and machine != 'first_available':
            data['machine'] = machine
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'application/octet-stream')}
                response = self.session.post(url, data=data, files=files, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    task_id = result['data']['task_ids'][0]
                    self._log(f"File submitted successfully: task {task_id}")
                    return task_id
                else:
                    error_msg = result.get('error_value', 'Unknown error')
                    raise CapeAPIError(f"Submission failed: {error_msg}")
            else:
                raise CapeAPIError(f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self._log(f"Error submitting file: {e}", 'error')
            raise CapeAPIError(f"Failed to submit file: {str(e)}")
    
    def get_task_status(self, task_id: int) -> Tuple[AnalysisStatus, str]:
        """Get task status"""
        url = f"{self.cape_url}/apiv2/tasks/view/{task_id}/"

        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                result = response.json()
                self._log(f"Raw status response: {result}")  # Debug log

                if not result.get('error') and result.get('data'):
                    # Handle different response structures
                    task_data = result['data']

                    # Try different ways to get task info
                    if isinstance(task_data, dict):
                        if 'task' in task_data:
                            task_info = task_data['task']
                        else:
                            task_info = task_data
                    else:
                        # If data is a list, get first item
                        task_info = task_data[0] if task_data else {}

                    status = task_info.get('status', 'unknown')

                    # Map Cape statuses to our statuses
                    status_mapping = {
                        'pending': AnalysisStatus.PENDING,
                        'running': AnalysisStatus.RUNNING,
                        'completed': AnalysisStatus.PROCESSING,
                        'reported': AnalysisStatus.REPORTED,
                        'failed': AnalysisStatus.FAILED
                    }

                    mapped_status = status_mapping.get(status, AnalysisStatus.PENDING)
                    message = f"Task {task_id} is {status}"

                    self._log(f"Task {task_id} status: {status}")
                    return mapped_status, message
                else:
                    error_msg = result.get('error_value', result.get('error', 'Unknown error'))
                    self._log(f"API error response: {error_msg}")
                    return AnalysisStatus.FAILED, f"Error getting status: {error_msg}"
            else:
                error_text = response.text[:200]  # Limit error text
                self._log(f"HTTP error {response.status_code}: {error_text}")
                return AnalysisStatus.FAILED, f"HTTP {response.status_code}: {error_text}"

        except Exception as e:
            self._log(f"Error getting task status: {e}", 'error')
            return AnalysisStatus.FAILED, f"Failed to get status: {str(e)}"
    
    def get_report(self, task_id: int, format: str = 'json') -> Optional[Dict[str, Any]]:
        """Get analysis report"""
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{format}/"
        
        try:
            response = self.session.get(url, timeout=60)
            if response.status_code == 200:
                if format in ['json', 'lite']:
                    report = response.json()
                    self._log(f"Retrieved {format} report for task {task_id}")
                    return report
                else:
                    return {'raw_report': response.text}
            elif response.status_code == 404:
                self._log(f"{format.upper()} report not found for task {task_id}, trying alternatives...")
                
                # Try alternative formats
                if format == 'json':
                    fallback_formats = ['lite']
                elif format == 'lite':
                    fallback_formats = ['json']
                else:
                    fallback_formats = ['lite', 'json']
                
                for fallback_format in fallback_formats:
                    fallback_url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{fallback_format}/"
                    try:
                        fallback_response = self.session.get(fallback_url, timeout=60)
                        if fallback_response.status_code == 200:
                            self._log(f"Retrieved {fallback_format} report as fallback for task {task_id}")
                            return fallback_response.json()
                    except:
                        continue
                
                return None
            else:
                self._log(f"Error getting report: HTTP {response.status_code}", 'error')
                return None
                
        except Exception as e:
            self._log(f"Error getting report: {e}", 'error')
            return None
    
    def wait_for_completion(self, task_id: int, poll_interval: int = 5) -> Tuple[AnalysisStatus, str]:
        """Wait for task completion with polling"""
        start_time = time.time()
        
        while time.time() - start_time < self.max_wait:
            status, message = self.get_task_status(task_id)
            
            if status in [AnalysisStatus.REPORTED, AnalysisStatus.FAILED]:
                return status, message
            
            if status == AnalysisStatus.PROCESSING:
                # Check if report is actually available
                report = self.get_report(task_id)
                if report:
                    return AnalysisStatus.REPORTED, "Analysis completed"
            
            time.sleep(poll_interval)
        
        return AnalysisStatus.FAILED, f"Timeout waiting for task {task_id} completion"
    
    def analyze_file(self, file_path: str, options: Dict[str, Any] = None, 
                    machine: str = None, force_reanalyze: bool = False) -> Dict[str, Any]:
        """
        Complete analysis pipeline: check existing → submit → wait → get results
        
        Args:
            file_path: Path to file to analyze
            options: Analysis options dict
            machine: Target machine
            force_reanalyze: Force new analysis even if existing results found
            
        Returns:
            dict with results or error info
        """
        try:
            # Calculate file hash for duplicate checking
            with open(file_path, 'rb') as f:
                import hashlib
                file_content = f.read()
                file_hash = hashlib.sha256(file_content).hexdigest()
            
            # Check for existing analysis unless forced
            if not force_reanalyze:
                existing_task_id = self.check_existing_analysis(file_hash)
                if existing_task_id:
                    self._log(f"Using existing analysis: task {existing_task_id}")
                    report = self.get_report(existing_task_id)
                    if report:
                        return {
                            'success': True,
                            'task_id': existing_task_id,
                            'was_cached': True,
                            'report': report
                        }
            
            # Submit new analysis
            task_id = self.submit_file(file_path, options, machine)
            if not task_id:
                return {'success': False, 'error': 'Failed to submit file'}
            
            # Wait for completion
            status, message = self.wait_for_completion(task_id)
            
            if status == AnalysisStatus.REPORTED:
                report = self.get_report(task_id)
                return {
                    'success': True,
                    'task_id': task_id,
                    'was_cached': False,
                    'report': report
                }
            else:
                return {
                    'success': False,
                    'task_id': task_id,
                    'error': message
                }
                
        except Exception as e:
            self._log(f"Analysis failed: {e}", 'error')
            return {'success': False, 'error': str(e)}

"""
Utility functions for CAPE Standalone Analyzer
"""
import os
import hashlib
import mimetypes
import logging
from typing import Dict, Any, Optional, Tuple
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

from models import FileInfo


logger = logging.getLogger(__name__)


def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions


def get_file_info(file: FileStorage, file_content: bytes = None) -> FileInfo:
    """Extract file information"""
    if file_content is None:
        file_content = file.read()
        file.seek(0)  # Reset file pointer
    
    file_info = FileInfo(
        filename=secure_filename(file.filename),
        size=len(file_content),
        mime_type=mimetypes.guess_type(file.filename)[0] or 'application/octet-stream'
    )
    
    # Calculate hashes
    file_info.calculate_hashes(file_content)
    
    return file_info


def save_uploaded_file(file: FileStorage, upload_folder: str, analysis_id: str) -> Tuple[str, bytes]:
    """Save uploaded file and return path and content"""
    # Create upload directory if it doesn't exist
    os.makedirs(upload_folder, exist_ok=True)
    
    # Read file content
    file_content = file.read()
    file.seek(0)  # Reset file pointer
    
    # Generate secure filename
    filename = secure_filename(file.filename)
    upload_filename = f"{analysis_id}_{filename}"
    file_path = os.path.join(upload_folder, upload_filename)
    
    # Save file
    with open(file_path, 'wb') as f:
        f.write(file_content)
    
    logger.info(f"File saved: {file_path}")
    return file_path, file_content


def cleanup_file(file_path: str) -> bool:
    """Clean up uploaded file"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"File cleaned up: {file_path}")
            return True
        return False
    except Exception as e:
        logger.error(f"Error cleaning up file {file_path}: {e}")
        return False


def parse_analysis_options(form_data: Dict[str, Any], mode: str) -> Dict[str, Any]:
    """Parse analysis options from form data"""
    options = {}
    
    if mode == 'advanced':
        # Parse advanced options
        option_mappings = {
            'procmemdump': 'process_memory',
            'procdump': 'process_dump',
            'import_reconstruction': 'import_reconstruction',
            'unpacker': 'unpacker',
            'syscall': 'syscall',
            'kernel_analysis': 'kernel_analysis',
            'norefer': 'norefer',
            'no-iat': 'oldloader',
            'unpack': 'unpack',
            'free': 'free',
            'nohuman': 'nohuman',
            'tor': 'tor',
            'mitmdump': 'mitmdump',
            'interactive': 'interactive',
            'manual': 'manual'
        }
        
        for cape_option, form_field in option_mappings.items():
            if form_data.get(form_field):
                if cape_option in ['unpacker', 'timeout', 'priority']:
                    # Numeric options
                    try:
                        options[cape_option] = str(int(form_data[form_field]))
                    except (ValueError, TypeError):
                        pass
                else:
                    # Boolean options
                    options[cape_option] = '1' if form_data[form_field] else '0'
        
        # Handle custom options string
        if form_data.get('custom_options'):
            custom_options = form_data['custom_options']
            for option in custom_options.split(','):
                if '=' in option:
                    key, value = option.split('=', 1)
                    options[key.strip()] = value.strip()
    
    else:
        # Default mode options
        options = {
            'procmemdump': '1',
            'import_reconstruction': '1',
            'unpacker': '2',
            'norefer': '1',
            'no-iat': '1'
        }
    
    return options


def create_success_response(data: Any, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response"""
    return {
        'success': True,
        'message': message,
        'data': data
    }


def create_error_response(message: str, error_code: str = None, details: Any = None) -> Dict[str, Any]:
    """Create standardized error response"""
    response = {
        'success': False,
        'error': True,
        'message': message
    }
    
    if error_code:
        response['error_code'] = error_code
    
    if details:
        response['details'] = details
    
    return response


def validate_file_upload(file: FileStorage, allowed_extensions: set, max_size: int) -> Optional[str]:
    """Validate uploaded file"""
    if not file or not file.filename:
        return "No file selected"
    
    if not allowed_file(file.filename, allowed_extensions):
        return f"File type not allowed. Allowed types: {', '.join(sorted(allowed_extensions))}"
    
    # Check file size (approximate)
    file.seek(0, 2)  # Seek to end
    size = file.tell()
    file.seek(0)  # Reset to beginning
    
    if size > max_size:
        return f"File too large. Maximum size: {max_size // (1024*1024)}MB"
    
    if size == 0:
        return "File is empty"
    
    return None


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace dangerous characters
    import re
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = filename.strip('. ')
    
    # Ensure filename is not empty
    if not filename:
        filename = 'unnamed_file'
    
    return filename


def get_machine_display_name(machine: str, machines_list: list) -> str:
    """Get display name for machine"""
    if machine == 'first_available':
        return 'First Available'
    
    for m in machines_list:
        if m.get('name') == machine:
            return f"{machine} ({m.get('platform', 'Unknown')})"
    
    return machine


def extract_report_summary(report: Dict[str, Any]) -> Dict[str, Any]:
    """Extract key information from analysis report"""
    if not report:
        return {}
    
    summary = {}
    
    # Basic info
    if 'info' in report:
        info = report['info']
        summary['analysis_id'] = info.get('id')
        summary['started'] = info.get('started')
        summary['ended'] = info.get('ended')
        summary['duration'] = info.get('duration')
    
    # Target info
    if 'target' in report:
        target = report['target']
        if 'file' in target:
            file_info = target['file']
            summary['filename'] = file_info.get('name')
            summary['file_size'] = file_info.get('size')
            summary['file_type'] = file_info.get('type')
            summary['md5'] = file_info.get('md5')
            summary['sha1'] = file_info.get('sha1')
            summary['sha256'] = file_info.get('sha256')
    
    # Malware score
    summary['malscore'] = report.get('malscore', 0)
    
    # Detection count
    if 'signatures' in report:
        summary['detections'] = len(report['signatures'])
    
    # Network activity
    if 'network' in report:
        network = report['network']
        summary['network_connections'] = len(network.get('tcp', []) + network.get('udp', []))
        summary['dns_requests'] = len(network.get('dns', []))
        summary['http_requests'] = len(network.get('http', []))
    
    # Process activity
    if 'behavior' in report and 'processes' in report['behavior']:
        summary['processes_created'] = len(report['behavior']['processes'])
    
    # CAPE extractions
    if 'CAPE' in report:
        summary['cape_extractions'] = len(report['CAPE'])
    
    return summary


def setup_logging(log_level: str, log_file: str = None) -> None:
    """Setup logging configuration"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        # Create log directory if it doesn't exist
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

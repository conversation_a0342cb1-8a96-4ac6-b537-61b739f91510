import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { List, Eye, FileText, RefreshCw, Plus } from 'lucide-react';

import { apiService } from '../services/api';
import StatusBadge from '../components/StatusBadge';

const AnalysesListPage = () => {
  const { data, isLoading, error, refetch } = useQuery(
    'analyses',
    apiService.listAnalyses,
    {
      refetchInterval: 10000, // Refetch every 10 seconds
    }
  );

  const analyses = data?.data?.analyses || [];

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <div className="spinner mr-3"></div>
          <span className="text-lg">Loading analyses...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="cape-card p-6">
          <h2 className="text-xl font-semibold text-red-600 mb-4">Error Loading Analyses</h2>
          <p className="text-gray-700 mb-4">{error.message}</p>
          <button onClick={() => refetch()} className="cape-button-primary">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <List className="h-6 w-6 mr-2" />
            All Analyses
          </h1>
          <p className="text-gray-600">
            {analyses.length} analysis{analyses.length !== 1 ? 'es' : ''} found
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => refetch()}
            className="cape-button-secondary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <Link to="/" className="cape-button-primary">
            <Plus className="h-4 w-4 mr-2" />
            New Analysis
          </Link>
        </div>
      </div>

      {/* Analyses Table */}
      {analyses.length === 0 ? (
        <div className="cape-card p-8 text-center">
          <List className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Analyses Found</h3>
          <p className="text-gray-600 mb-4">
            You haven't submitted any files for analysis yet.
          </p>
          <Link to="/" className="cape-button-primary">
            <Plus className="h-4 w-4 mr-2" />
            Submit Your First File
          </Link>
        </div>
      ) : (
        <div className="cape-card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="cape-table">
              <thead>
                <tr>
                  <th>File</th>
                  <th>Status</th>
                  <th>Size</th>
                  <th>Submitted</th>
                  <th>Duration</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {analyses.map((analysis) => {
                  const duration = analysis.started_at && analysis.completed_at
                    ? Math.round((new Date(analysis.completed_at) - new Date(analysis.started_at)) / 1000)
                    : null;

                  return (
                    <tr key={analysis.analysis_id}>
                      <td>
                        <div>
                          <div className="font-medium text-gray-900">
                            {analysis.filename}
                          </div>
                          <div className="text-xs text-gray-500 font-mono">
                            {analysis.file_info?.sha256?.substring(0, 16)}...
                          </div>
                        </div>
                      </td>
                      <td>
                        <StatusBadge status={analysis.status} size="small" />
                      </td>
                      <td className="text-gray-900">
                        {formatFileSize(analysis.file_info?.size)}
                      </td>
                      <td className="text-gray-900">
                        {formatDateTime(analysis.created_at)}
                      </td>
                      <td className="text-gray-900">
                        {duration ? `${duration}s` : 'N/A'}
                      </td>
                      <td>
                        <div className="flex space-x-2">
                          <Link
                            to={`/analysis/${analysis.analysis_id}`}
                            className="text-blue-600 hover:text-blue-800 transition-colors"
                            title="View Status"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          {analysis.status === 'reported' && (
                            <Link
                              to={`/report/${analysis.analysis_id}`}
                              className="text-green-600 hover:text-green-800 transition-colors"
                              title="View Report"
                            >
                              <FileText className="h-4 w-4" />
                            </Link>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysesListPage;

#!/bin/bash

echo "============================================================"
echo "CAPE Malware Analyzer - Standalone Web Service"
echo "============================================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create virtual environment"
        exit 1
    fi
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install/upgrade dependencies
echo "Installing dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

# Set default environment variables if not set
export CAPE_URL=${CAPE_URL:-"http://localhost:8000"}
export PORT=${PORT:-8080}

echo
echo "Configuration:"
echo "  CAPE_URL: $CAPE_URL"
echo "  PORT: $PORT"
echo

# Start the application
echo "Starting CAPE Malware Analyzer..."
python run.py
